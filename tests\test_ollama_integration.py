"""Integration tests for Ollama functionality."""

import pytest
import asyncio
from unittest.mock import Mock, patch
from src.symbolic_reasoning import Sym<PERSON><PERSON>easoner
from src.retrieval import Retriever

class TestOllamaIntegration:
    """Test Ollama integrates with existing architecture."""

    def test_ollama_engine_initialization(self):
        """Test Ollama engine can be initialized."""
        # Test existing engines still work
        local_reasoner = SymbolicReasoner(engine="local")
        assert local_reasoner.engine == "local"
        
        # Test new Ollama engine
        try:
            ollama_reasoner = SymbolicReasoner(engine="ollama", model="llama3.2")
            assert ollama_reasoner.engine == "ollama"
            assert ollama_reasoner.model == "llama3.2"
            assert hasattr(ollama_reasoner, 'ollama_client')
        except Exception as e:
            pytest.skip(f"Ollama not available: {e}")

    @pytest.mark.asyncio
    async def test_ollama_query_processing(self):
        """Test Ollama can process queries."""
        try:
            reasoner = SymbolicReasoner(engine="ollama", model="llama3.2")
            
            # Mock the Ollama client to avoid requiring actual Ollama server
            with patch.object(reasoner, 'ollama_client') as mock_client:
                mock_client.chat.return_value = {
                    'message': {'content': 'Test response from Ollama'}
                }
                
                response = reasoner.process_query("Test query")
                assert isinstance(response, str)
                assert len(response) > 0
                
        except Exception as e:
            pytest.skip(f"Ollama not available: {e}")

    def test_ollama_fallback_mechanism(self):
        """Test fallback to basic reasoning when Ollama fails."""
        reasoner = SymbolicReasoner(engine="ollama", model="llama3.2")
        
        # Mock Ollama client to raise an exception
        with patch.object(reasoner, 'ollama_client') as mock_client:
            mock_client.chat.side_effect = Exception("Ollama connection failed")
            
            # Should fallback to basic reasoning
            response = reasoner.process_query("What is symbolic reasoning?")
            assert isinstance(response, str)
            assert "symbolic reasoning" in response.lower()

    def test_retriever_ollama_embeddings(self):
        """Test retriever can use Ollama embeddings."""
        try:
            retriever = Retriever(vector_db="faiss", embedding_backend="ollama")
            assert retriever.embedding_backend == "ollama"
            assert hasattr(retriever, 'ollama_client')
        except Exception as e:
            pytest.skip(f"Ollama not available: {e}")

    @pytest.mark.asyncio
    async def test_retriever_embedding_generation(self):
        """Test retriever can generate embeddings with Ollama."""
        retriever = Retriever(vector_db="faiss", embedding_backend="ollama")
        
        # Mock the Ollama client
        with patch.object(retriever, 'ollama_client') as mock_client:
            mock_client.embed.return_value = {
                'embeddings': [[0.1, 0.2, 0.3] * 256]  # 768-dim embedding
            }
            
            embeddings = await retriever._generate_embeddings(["test text"])
            assert embeddings.shape == (1, 768)

    def test_existing_functionality_preserved(self):
        """Test that existing functionality still works."""
        # Test local engine
        local_reasoner = SymbolicReasoner(engine="local")
        response = local_reasoner.process_query("What is symbolic reasoning?")
        assert isinstance(response, str)
        assert len(response) > 0
        
        # Test retriever with random embeddings
        retriever = Retriever(vector_db="faiss", embedding_backend="random")
        assert retriever.embedding_backend == "random"
        
        # Test adding documents
        documents = [
            {"id": "1", "text": "Test document 1"},
            {"id": "2", "text": "Test document 2"}
        ]
        retriever.add_documents(documents)
        
        # Test search
        results = retriever.search("test", k=1)
        assert len(results) <= 1

    def test_configuration_integration(self):
        """Test configuration works with Ollama settings."""
        from src.core.config import ModelSettings
        
        # Test that Ollama settings are available
        settings = ModelSettings()
        assert hasattr(settings, 'ollama_host')
        assert hasattr(settings, 'ollama_timeout')
        assert settings.ollama_host == "http://localhost:11434"
        assert settings.ollama_timeout == 300

    @pytest.mark.asyncio
    async def test_api_endpoints_with_ollama(self):
        """Test API endpoints work with Ollama engine."""
        from fastapi.testclient import TestClient
        from src.main import app
        
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/health")
        assert response.status_code == 200
        
        # Test system info endpoint
        response = client.get("/system/info")
        assert response.status_code == 200
        
        # Test Ollama-specific endpoints
        response = client.get("/ollama/models")
        # Should return either models list or error (depending on Ollama availability)
        assert response.status_code == 200
        data = response.json()
        assert "models" in data or "error" in data

    def test_error_handling(self):
        """Test proper error handling for Ollama failures."""
        # Test initialization with missing Ollama
        with patch('src.symbolic_reasoning.ollama', side_effect=ImportError):
            with pytest.raises(Exception):  # Should raise ConfigurationError
                SymbolicReasoner(engine="ollama")

    def test_gpu_integration(self):
        """Test GPU integration works with Ollama."""
        import torch
        
        if torch.cuda.is_available():
            reasoner = SymbolicReasoner(engine="ollama", use_gpu=True)
            assert reasoner.use_gpu == True
        else:
            reasoner = SymbolicReasoner(engine="ollama", use_gpu=False)
            assert reasoner.use_gpu == False

    def test_system_info_includes_ollama(self):
        """Test system info includes Ollama details when using Ollama engine."""
        try:
            reasoner = SymbolicReasoner(engine="ollama")
            info = reasoner.get_system_info()
            
            assert info['engine'] == 'ollama'
            assert 'model' in info
            assert 'gpu_enabled' in info
            
        except Exception as e:
            pytest.skip(f"Ollama not available: {e}")

if __name__ == "__main__":
    pytest.main([__file__])
