"""Test configuration and fixtures."""

import pytest
from unittest.mock import MagicMock
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.append(str(src_path))

# Mock dependencies
class MockSymbolicReasoner:
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
    
    def process_query(self, query, context=None):
        return "Test response"
    
    def get_system_info(self):
        return {
            "engine": "test",
            "model": "test",
            "gpu_enabled": False,
            "gpu_available": False,
            "gpu_name": None
        }

class MockRetriever:
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
        self.documents = {}
    
    def add_documents(self, documents):
        for doc in documents:
            self.documents[doc['id']] = doc['text']
    
    def search(self, query, k=5):
        return [{"text": "Test document", "score": 0.95}]
    
    def get_system_info(self):
        return {
            "vector_db": "test",
            "gpu_enabled": False,
            "gpu_available": False,
            "gpu_name": None,
            "index_size": len(self.documents)
        }

@pytest.fixture
def mock_reasoner():
    return MockSymbolicReasoner()

@pytest.fixture
def mock_retriever():
    return MockRetriever()

# Mock the imports in main.py
sys.modules['symbolic_reasoning'] = MagicMock()
sys.modules['symbolic_reasoning'].SymbolicReasoner = MockSymbolicReasoner
sys.modules['retrieval'] = MagicMock()
sys.modules['retrieval'].Retriever = MockRetriever
