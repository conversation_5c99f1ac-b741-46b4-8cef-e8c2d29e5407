version: '3'

services:
  neural-symbolic-ai:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./.symai:/app/.symai
    environment:
      - PYTHONPATH=/app
      - OMP_NUM_THREADS=1
      - OPENBLAS_NUM_THREADS=1
      - M<PERSON>L_NUM_THREADS=1
      - VECLIB_MAXIMUM_THREADS=1
      - NUMEXPR_NUM_THREADS=1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/performance"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
