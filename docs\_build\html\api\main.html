

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Main Application Module &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Symbolic Reasoning Module" href="symbolic_reasoning.html" />
    <link rel="prev" title="API Reference" href="../modules.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../modules.html">API Reference</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../modules.html#core-modules">Core Modules</a><ul class="current">
<li class="toctree-l3 current"><a class="current reference internal" href="#">Main Application Module</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#main.add_security_headers"><code class="docutils literal notranslate"><span class="pre">add_security_headers()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.check_request_size_middleware"><code class="docutils literal notranslate"><span class="pre">check_request_size_middleware()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.rate_limit_middleware"><code class="docutils literal notranslate"><span class="pre">rate_limit_middleware()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.symbolic_ai_exception_handler"><code class="docutils literal notranslate"><span class="pre">symbolic_ai_exception_handler()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.validation_exception_handler"><code class="docutils literal notranslate"><span class="pre">validation_exception_handler()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.authentication_exception_handler"><code class="docutils literal notranslate"><span class="pre">authentication_exception_handler()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.general_exception_handler"><code class="docutils literal notranslate"><span class="pre">general_exception_handler()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.read_root"><code class="docutils literal notranslate"><span class="pre">read_root()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.api_info"><code class="docutils literal notranslate"><span class="pre">api_info()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.ResponseCache"><code class="docutils literal notranslate"><span class="pre">ResponseCache</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.LegacyChatRequest"><code class="docutils literal notranslate"><span class="pre">LegacyChatRequest</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.LegacyChatResponse"><code class="docutils literal notranslate"><span class="pre">LegacyChatResponse</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.optimize_gpu_memory"><code class="docutils literal notranslate"><span class="pre">optimize_gpu_memory()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.process_query"><code class="docutils literal notranslate"><span class="pre">process_query()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.add_document"><code class="docutils literal notranslate"><span class="pre">add_document()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.get_document_count"><code class="docutils literal notranslate"><span class="pre">get_document_count()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.chat"><code class="docutils literal notranslate"><span class="pre">chat()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.chat_stream"><code class="docutils literal notranslate"><span class="pre">chat_stream()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.clean_cache"><code class="docutils literal notranslate"><span class="pre">clean_cache()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.openai_chat_stream"><code class="docutils literal notranslate"><span class="pre">openai_chat_stream()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.openai_chat"><code class="docutils literal notranslate"><span class="pre">openai_chat()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.performance_stats"><code class="docutils literal notranslate"><span class="pre">performance_stats()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.system_info"><code class="docutils literal notranslate"><span class="pre">system_info()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#main.shutdown_event"><code class="docutils literal notranslate"><span class="pre">shutdown_event()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#fastapi-application">FastAPI Application</a></li>
<li class="toctree-l4"><a class="reference internal" href="#endpoints">Endpoints</a></li>
<li class="toctree-l4"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#security">Security</a></li>
<li class="toctree-l4"><a class="reference internal" href="#example-usage">Example Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="#error-handling">Error Handling</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="models.html">Data Models Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="../modules.html#module-main">Core Components</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#utility-modules">Utility Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#api-routes">API Routes</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Main Application Module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#main.add_security_headers"><code class="docutils literal notranslate"><span class="pre">add_security_headers()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.check_request_size_middleware"><code class="docutils literal notranslate"><span class="pre">check_request_size_middleware()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.rate_limit_middleware"><code class="docutils literal notranslate"><span class="pre">rate_limit_middleware()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.symbolic_ai_exception_handler"><code class="docutils literal notranslate"><span class="pre">symbolic_ai_exception_handler()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.validation_exception_handler"><code class="docutils literal notranslate"><span class="pre">validation_exception_handler()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.authentication_exception_handler"><code class="docutils literal notranslate"><span class="pre">authentication_exception_handler()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.general_exception_handler"><code class="docutils literal notranslate"><span class="pre">general_exception_handler()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.read_root"><code class="docutils literal notranslate"><span class="pre">read_root()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.api_info"><code class="docutils literal notranslate"><span class="pre">api_info()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.ResponseCache"><code class="docutils literal notranslate"><span class="pre">ResponseCache</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#main.ResponseCache.__init__"><code class="docutils literal notranslate"><span class="pre">ResponseCache.__init__()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#main.ResponseCache.get"><code class="docutils literal notranslate"><span class="pre">ResponseCache.get()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#main.ResponseCache.set"><code class="docutils literal notranslate"><span class="pre">ResponseCache.set()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#main.ResponseCache.clean"><code class="docutils literal notranslate"><span class="pre">ResponseCache.clean()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#main.ResponseCache.size"><code class="docutils literal notranslate"><span class="pre">ResponseCache.size()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#main.LegacyChatRequest"><code class="docutils literal notranslate"><span class="pre">LegacyChatRequest</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#main.LegacyChatRequest.text"><code class="docutils literal notranslate"><span class="pre">LegacyChatRequest.text</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#main.LegacyChatRequest.model_config"><code class="docutils literal notranslate"><span class="pre">LegacyChatRequest.model_config</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#main.LegacyChatResponse"><code class="docutils literal notranslate"><span class="pre">LegacyChatResponse</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#main.LegacyChatResponse.response"><code class="docutils literal notranslate"><span class="pre">LegacyChatResponse.response</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#main.LegacyChatResponse.cached"><code class="docutils literal notranslate"><span class="pre">LegacyChatResponse.cached</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#main.LegacyChatResponse.model_config"><code class="docutils literal notranslate"><span class="pre">LegacyChatResponse.model_config</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#main.optimize_gpu_memory"><code class="docutils literal notranslate"><span class="pre">optimize_gpu_memory()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.process_query"><code class="docutils literal notranslate"><span class="pre">process_query()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.add_document"><code class="docutils literal notranslate"><span class="pre">add_document()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.get_document_count"><code class="docutils literal notranslate"><span class="pre">get_document_count()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.chat"><code class="docutils literal notranslate"><span class="pre">chat()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.chat_stream"><code class="docutils literal notranslate"><span class="pre">chat_stream()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.clean_cache"><code class="docutils literal notranslate"><span class="pre">clean_cache()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.openai_chat_stream"><code class="docutils literal notranslate"><span class="pre">openai_chat_stream()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.openai_chat"><code class="docutils literal notranslate"><span class="pre">openai_chat()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.performance_stats"><code class="docutils literal notranslate"><span class="pre">performance_stats()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.system_info"><code class="docutils literal notranslate"><span class="pre">system_info()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#main.shutdown_event"><code class="docutils literal notranslate"><span class="pre">shutdown_event()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#fastapi-application">FastAPI Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="#endpoints">Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#chat-completions">Chat Completions</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-monitoring">Performance Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="#system-information">System Information</a></li>
<li class="toctree-l3"><a class="reference internal" href="#health-checks">Health Checks</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="#security">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="#example-usage">Example Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../modules.html">API Reference</a></li>
      <li class="breadcrumb-item active">Main Application Module</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/main.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-main">
<span id="main-application-module"></span><h1>Main Application Module<a class="headerlink" href="#module-main" title="Link to this heading"></a></h1>
<p>Main application module for the Neural Symbolic Language Model.
This module provides the FastAPI server implementation and core API endpoints.</p>
<dl class="py function">
<dt class="sig sig-object py" id="main.add_security_headers">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">add_security_headers</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">call_next</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#add_security_headers"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.add_security_headers" title="Link to this definition"></a></dt>
<dd><p>Add security headers to all responses.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.check_request_size_middleware">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">check_request_size_middleware</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">call_next</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#check_request_size_middleware"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.check_request_size_middleware" title="Link to this definition"></a></dt>
<dd><p>Check request size limits.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.rate_limit_middleware">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">rate_limit_middleware</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">call_next</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#rate_limit_middleware"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.rate_limit_middleware" title="Link to this definition"></a></dt>
<dd><p>Apply rate limiting.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.symbolic_ai_exception_handler">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">symbolic_ai_exception_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#exceptions.SymbolicAIException" title="exceptions.SymbolicAIException"><span class="pre">SymbolicAIException</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#symbolic_ai_exception_handler"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.symbolic_ai_exception_handler" title="Link to this definition"></a></dt>
<dd><p>Handle SymbolicAI custom exceptions.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.validation_exception_handler">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">validation_exception_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#exceptions.ValidationError" title="exceptions.ValidationError"><span class="pre">ValidationError</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#validation_exception_handler"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.validation_exception_handler" title="Link to this definition"></a></dt>
<dd><p>Handle validation errors.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.authentication_exception_handler">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">authentication_exception_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#exceptions.AuthenticationError" title="exceptions.AuthenticationError"><span class="pre">AuthenticationError</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#authentication_exception_handler"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.authentication_exception_handler" title="Link to this definition"></a></dt>
<dd><p>Handle authentication errors.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.general_exception_handler">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">general_exception_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#Exception" title="(in Python v3.13)"><span class="pre">Exception</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#general_exception_handler"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.general_exception_handler" title="Link to this definition"></a></dt>
<dd><p>Handle all other exceptions.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.read_root">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">read_root</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#read_root"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.read_root" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.api_info">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">api_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#api_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.api_info" title="Link to this definition"></a></dt>
<dd><p>Get API information and available endpoints.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="main.ResponseCache">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">ResponseCache</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">max_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1000</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#ResponseCache"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.ResponseCache" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="main.ResponseCache.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">max_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1000</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#ResponseCache.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.ResponseCache.__init__" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="main.ResponseCache.get">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#ResponseCache.get"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.ResponseCache.get" title="Link to this definition"></a></dt>
<dd><p>Get a value from the cache.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>key</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The cache key</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The cached value, or None if not found</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="main.ResponseCache.set">
<span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#ResponseCache.set"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.ResponseCache.set" title="Link to this definition"></a></dt>
<dd><p>Set a value in the cache.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>key</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The cache key</p></li>
<li><p><strong>value</strong> – The value to cache</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="main.ResponseCache.clean">
<span class="sig-name descname"><span class="pre">clean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#ResponseCache.clean"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.ResponseCache.clean" title="Link to this definition"></a></dt>
<dd><p>Clean old entries from the cache.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>count</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a><em>, </em><em>optional</em>) – Number of entries to remove</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="main.ResponseCache.size">
<span class="sig-name descname"><span class="pre">size</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#ResponseCache.size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.ResponseCache.size" title="Link to this definition"></a></dt>
<dd><p>Get the current cache size.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The number of entries in the cache</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="main.LegacyChatRequest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">LegacyChatRequest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#LegacyChatRequest"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.LegacyChatRequest" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="main.LegacyChatRequest.text">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#main.LegacyChatRequest.text" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="main.LegacyChatRequest.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{}</span></em><a class="headerlink" href="#main.LegacyChatRequest.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="main.LegacyChatResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">LegacyChatResponse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">cached</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#LegacyChatResponse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.LegacyChatResponse" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="main.LegacyChatResponse.response">
<span class="sig-name descname"><span class="pre">response</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#main.LegacyChatResponse.response" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="main.LegacyChatResponse.cached">
<span class="sig-name descname"><span class="pre">cached</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#main.LegacyChatResponse.cached" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="main.LegacyChatResponse.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{}</span></em><a class="headerlink" href="#main.LegacyChatResponse.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.optimize_gpu_memory">
<span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">optimize_gpu_memory</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#optimize_gpu_memory"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.optimize_gpu_memory" title="Link to this definition"></a></dt>
<dd><p>Configure PyTorch for optimal GPU memory usage</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.process_query">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">process_query</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/main.html#process_query"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.process_query" title="Link to this definition"></a></dt>
<dd><p>Process a query using both retrieval and reasoning components.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>query</strong> – The user’s query text</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The generated response</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.add_document">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">add_document</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.DocumentAddRequest" title="models.DocumentAddRequest"><span class="pre">DocumentAddRequest</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#add_document"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.add_document" title="Link to this definition"></a></dt>
<dd><p>Add a document to the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – DocumentAddRequest object containing the document content</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>DocumentAddResponse object indicating success</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.get_document_count">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">get_document_count</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#get_document_count"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.get_document_count" title="Link to this definition"></a></dt>
<dd><p>Get the number of documents in the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Number of documents</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.chat">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">chat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.ChatRequest" title="models.ChatRequest"><span class="pre">ChatRequest</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">background_tasks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/background/#fastapi.BackgroundTasks" title="(in FastAPI v0.0.0)"><span class="pre">BackgroundTasks</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#chat"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.chat" title="Link to this definition"></a></dt>
<dd><p>Process a chat request.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>request</strong> – ChatRequest object containing the text query</p></li>
<li><p><strong>background_tasks</strong> – FastAPI BackgroundTasks for async operations</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Response from the system</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="../modules.html#models.ChatResponse" title="models.ChatResponse">ChatResponse</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.chat_stream">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">chat_stream</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.ChatRequest" title="models.ChatRequest"><span class="pre">ChatRequest</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#chat_stream"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.chat_stream" title="Link to this definition"></a></dt>
<dd><p>Process a chat request with streaming response.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – ChatRequest object containing the text query</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Streaming response generator</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>StreamingResponse</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.clean_cache">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">clean_cache</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#clean_cache"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.clean_cache" title="Link to this definition"></a></dt>
<dd><p>Clean old entries from the response cache.</p>
<p>Keeps the cache size manageable by removing oldest entries.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.openai_chat_stream">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">openai_chat_stream</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.OpenAIChatRequest" title="models.OpenAIChatRequest"><span class="pre">OpenAIChatRequest</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#openai_chat_stream"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.openai_chat_stream" title="Link to this definition"></a></dt>
<dd><p>Handle streaming for OpenAI-compatible chat endpoint.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – OpenAIChatRequest object</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Server-sent events stream</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>StreamingResponse</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.openai_chat">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">openai_chat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.OpenAIChatRequest" title="models.OpenAIChatRequest"><span class="pre">OpenAIChatRequest</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#openai_chat"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.openai_chat" title="Link to this definition"></a></dt>
<dd><p>OpenAI-compatible chat endpoint.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – OpenAIChatRequest object</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>OpenAIChatResponse object or StreamingResponse if streaming</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.performance_stats">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">performance_stats</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#performance_stats"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.performance_stats" title="Link to this definition"></a></dt>
<dd><p>Get performance statistics for the system.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Performance statistics</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.system_info">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">system_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#system_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.system_info" title="Link to this definition"></a></dt>
<dd><p>Get system configuration information.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="main.shutdown_event">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">main.</span></span><span class="sig-name descname"><span class="pre">shutdown_event</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/main.html#shutdown_event"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#main.shutdown_event" title="Link to this definition"></a></dt>
<dd><p>Perform cleanup on shutdown.</p>
</dd></dl>

<section id="fastapi-application">
<h2>FastAPI Application<a class="headerlink" href="#fastapi-application" title="Link to this heading"></a></h2>
<p>The main FastAPI application provides OpenAI-compatible endpoints for chat completions,
performance monitoring, and system information.</p>
<p>Key Features:
* OpenAI-compatible chat completions API
* Streaming response support
* Performance monitoring endpoints
* Health check endpoints
* Automatic API documentation</p>
</section>
<section id="endpoints">
<h2>Endpoints<a class="headerlink" href="#endpoints" title="Link to this heading"></a></h2>
<section id="chat-completions">
<h3>Chat Completions<a class="headerlink" href="#chat-completions" title="Link to this heading"></a></h3>
</section>
<section id="performance-monitoring">
<h3>Performance Monitoring<a class="headerlink" href="#performance-monitoring" title="Link to this heading"></a></h3>
</section>
<section id="system-information">
<h3>System Information<a class="headerlink" href="#system-information" title="Link to this heading"></a></h3>
</section>
<section id="health-checks">
<h3>Health Checks<a class="headerlink" href="#health-checks" title="Link to this heading"></a></h3>
</section>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<p>The application uses environment-based configuration with the following key settings:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">APP_HOST</span></code>: Server host address (default: 0.0.0.0)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">APP_PORT</span></code>: Server port (default: 8000)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">APP_DEBUG</span></code>: Debug mode (default: False)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">APP_WORKERS</span></code>: Number of worker processes (default: 1)</p></li>
</ul>
</section>
<section id="security">
<h2>Security<a class="headerlink" href="#security" title="Link to this heading"></a></h2>
<p>All endpoints are protected by:</p>
<ul class="simple">
<li><p>API key authentication</p></li>
<li><p>Rate limiting</p></li>
<li><p>Input validation</p></li>
<li><p>Request size limits</p></li>
<li><p>CORS configuration</p></li>
</ul>
</section>
<section id="example-usage">
<h2>Example Usage<a class="headerlink" href="#example-usage" title="Link to this heading"></a></h2>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span><span class="w"> </span><span class="nn">requests</span>

<span class="c1"># Chat completion request</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/v1/chat/completions&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer your-api-key&quot;</span><span class="p">},</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;local&quot;</span><span class="p">,</span>
        <span class="s2">&quot;messages&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="p">{</span><span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;Explain symbolic reasoning&quot;</span><span class="p">}</span>
        <span class="p">],</span>
        <span class="s2">&quot;max_tokens&quot;</span><span class="p">:</span> <span class="mi">150</span><span class="p">,</span>
        <span class="s2">&quot;temperature&quot;</span><span class="p">:</span> <span class="mf">0.7</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="n">result</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="n">result</span><span class="p">[</span><span class="s2">&quot;choices&quot;</span><span class="p">][</span><span class="mi">0</span><span class="p">][</span><span class="s2">&quot;message&quot;</span><span class="p">][</span><span class="s2">&quot;content&quot;</span><span class="p">])</span>
</pre></div>
</div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Streaming response</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/v1/chat/completions&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer your-api-key&quot;</span><span class="p">},</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;local&quot;</span><span class="p">,</span>
        <span class="s2">&quot;messages&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="p">{</span><span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;What is AI?&quot;</span><span class="p">}</span>
        <span class="p">],</span>
        <span class="s2">&quot;stream&quot;</span><span class="p">:</span> <span class="kc">True</span>
    <span class="p">},</span>
    <span class="n">stream</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">iter_lines</span><span class="p">():</span>
    <span class="k">if</span> <span class="n">line</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">line</span><span class="o">.</span><span class="n">decode</span><span class="p">())</span>
</pre></div>
</div>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>The API returns structured error responses:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid request format&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;field&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;messages&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Messages array cannot be empty&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Common error codes:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">VALIDATION_ERROR</span></code> (400): Invalid request format or parameters</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">AUTHENTICATION_ERROR</span></code> (401): Invalid or missing API key</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">RATE_LIMIT_ERROR</span></code> (429): Rate limit exceeded</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INTERNAL_ERROR</span></code> (500): Server error</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../modules.html" class="btn btn-neutral float-left" title="API Reference" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="symbolic_reasoning.html" class="btn btn-neutral float-right" title="Symbolic Reasoning Module" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>