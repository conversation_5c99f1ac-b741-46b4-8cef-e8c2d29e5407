"""
LightRAG-based retrieval module for the Neural Symbolic Language Model.
This module provides a wrapper around LightRAG for efficient retrieval-augmented generation.
"""

import torch
from typing import List, Dict, Optional, Union
import numpy as np
import logging
from vector_store import TorchVectorStore

# Configure logger
logger = logging.getLogger(__name__)

# Try to import FAISS with graceful fallback
try:
    import faiss
    FAISS_AVAILABLE = True
    logger.info("Imported FAISS successfully")
    
    # Check if GPU FAISS is available
    GPU_FAISS_AVAILABLE = hasattr(faiss, 'StandardGpuResources')
    if GPU_FAISS_AVAILABLE:
        logger.info(f"GPU FAISS is available")
    else:
        logger.info("Using CPU version of FAISS")
        
except ImportError:
    logger.warning("FAISS not available. Using fallback vector storage.")
    FAISS_AVAILABLE = False
    GPU_FAISS_AVAILABLE = False

class Retriever:
    def __init__(self, vector_db="faiss", use_gpu=True):
        """Initialize the retrieval system.
        
        Args:
            vector_db (str): The vector database to use (e.g., "faiss", "chromadb")
            use_gpu (bool): Whether to use GPU acceleration if available
        """
        # Check GPU availability
        gpu_available = torch.cuda.is_available()
        if use_gpu and not gpu_available:
            print("WARNING: GPU requested but not available. Falling back to CPU.")
        self.use_gpu = use_gpu and gpu_available
        self.vector_db = vector_db
        self.index = None
        self.documents = {}
        self.setup_vector_db()
    
    def setup_vector_db(self):
        """Set up the vector database based on the selected backend."""
        self.dimension = 768  # Default BERT embedding dimension
        self.using_torch_fallback = False
        
        # Check if FAISS is available
        if not FAISS_AVAILABLE:
            logger.warning("FAISS not available. Using PyTorch vector store fallback.")
            self.index = TorchVectorStore(dimension=self.dimension, use_gpu=self.use_gpu)
            self.using_torch_fallback = True
            if self.use_gpu and torch.cuda.is_available():
                logger.info(f"Using PyTorch GPU vector store on: {torch.cuda.get_device_name(0)}")
            return
            
        # FAISS is available
        if self.vector_db == "faiss":
            # Create the base index
            self.index = faiss.IndexFlatL2(self.dimension)
            
            # Try to use GPU FAISS if available and requested
            if self.use_gpu and torch.cuda.is_available():
                if GPU_FAISS_AVAILABLE:
                    try:
                        logger.info("Setting up GPU-optimized FAISS index")
                        # Get GPU resources
                        res = faiss.StandardGpuResources()
                        
                        # Move the index to GPU
                        self.index = faiss.index_cpu_to_gpu(res, 0, self.index)
                        logger.info(f"FAISS index using GPU: {torch.cuda.get_device_name(0)}")
                    except Exception as e:
                        logger.warning(f"Error setting up GPU FAISS: {str(e)}")
                        logger.info("Falling back to PyTorch GPU vector store")
                        self.index = TorchVectorStore(dimension=self.dimension, use_gpu=True)
                        self.using_torch_fallback = True
                else:
                    # No GPU FAISS but GPU is available, use PyTorch fallback
                    logger.info("GPU FAISS not available but GPU detected. Using PyTorch GPU vector store.")
                    self.index = TorchVectorStore(dimension=self.dimension, use_gpu=True)
                    self.using_torch_fallback = True
            else:
                # CPU mode
                logger.info("Using CPU FAISS")
                self.use_gpu = False
        else:
            raise ValueError(f"Unsupported vector database: {self.vector_db}")
    
    def add_documents(self, documents: List[Dict[str, str]], embeddings: Optional[np.ndarray] = None):
        """Add documents to the retrieval system.
        
        Args:
            documents: List of document dictionaries with 'id' and 'text' keys
            embeddings: Optional pre-computed embeddings for the documents
        """
        if not documents:
            return
            
        if embeddings is None:
            # TODO: Implement document embedding using a language model
            # For now, use random embeddings for testing
            embeddings = np.random.randn(len(documents), self.dimension).astype('float32')
        
        # Add documents to the index based on the backend type
        if self.using_torch_fallback:
            # Extract document IDs and texts
            doc_ids = [doc['id'] for doc in documents]
            texts = [doc['text'] for doc in documents]
            
            # Use the TorchVectorStore add method
            self.index.add(embeddings, doc_ids, texts)
            
            # Store document metadata in our local dictionary as well
            for doc in documents:
                self.documents[doc['id']] = doc['text']
        else:
            # Using FAISS
            try:
                # Add embeddings to FAISS index
                self.index.add(embeddings)
                
                # Store document metadata
                for doc in documents:
                    self.documents[doc['id']] = doc['text']
            except Exception as e:
                logger.error(f"Error adding documents to FAISS index: {str(e)}")
                return
    
    def search(self, query: str, k: int = 5) -> List[Dict[str, Union[str, float]]]:
        """Search for relevant documents given a query.
        
        Args:
            query (str): The search query
            k (int): Number of results to return
            
        Returns:
            List of dictionaries containing document text and relevance scores
        """
        # TODO: Implement query embedding using a language model
        # For now, use a random vector for testing
        query_vector = np.random.randn(1, self.dimension).astype('float32')
        
        # Handle search differently based on the backend
        if self.using_torch_fallback:
            # For TorchVectorStore
            try:
                results = self.index.search(query_vector, k)
                # Results already in the right format
                return results
            except Exception as e:
                logger.error(f"Error searching with TorchVectorStore: {str(e)}")
                return []
        else:
            # For FAISS
            try:
                # If the index is not initialized or empty
                if self.index is None or len(self.documents) == 0:
                    return []
                
                # Perform the search
                distances, indices = self.index.search(query_vector, k)
                
                # Format results
                results = []
                for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
                    if idx < 0 or idx >= len(self.documents):
                        continue
                    doc_id = list(self.documents.keys())[idx]
                    results.append({
                        'id': doc_id,
                        'text': self.documents[doc_id],
                        'score': float(1.0 / (1.0 + dist))  # Convert distance to similarity score
                    })
                
                return results
            except Exception as e:
                logger.error(f"Error searching with FAISS: {str(e)}")
                return []
    
    def get_system_info(self) -> Dict[str, Union[str, bool]]:
        """Get information about the system configuration.
        
        Returns:
            dict: System configuration information
        """
        return {
            'vector_db': self.vector_db,
            'gpu_enabled': self.use_gpu,
            'gpu_available': torch.cuda.is_available(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
            "index_size": len(self.documents)
        }

    def optimize_index(self):
        """Optimize the FAISS index for better performance.
        
        This should be called after adding a significant number of documents.
        """
        if not self.use_gpu:
            return True
            
        try:
            # Clear any unused memory
            torch.cuda.empty_cache()
            
            # If index is not trained (for IVF indexes), train it
            if hasattr(self.index, 'is_trained') and not self.index.is_trained:
                # Generate training data
                train_size = min(len(self.documents), 100000)
                train_data = np.random.randn(train_size, self.dimension).astype('float32')
                self.index.train(train_data)
                print("Trained IVF index")
            
            # If using GPU, ensure we're using float16 for better performance
            if hasattr(self.index, 'getNumProbes'):
                # For IVF indexes, set number of probes for better recall
                self.index.nprobe = min(32, self.index.nlist)
            
            return True
            
        except Exception as e:
            print(f"Error optimizing index: {str(e)}")
            return False

    def batch_add_documents(self, documents, batch_size=32):
        """Add multiple documents in batches for better performance.
        
        Args:
            documents (list): List of documents to add
            batch_size (int): Size of each batch
            
        Returns:
            bool: True if successful
        """
        try:
            # Pre-allocate GPU memory for better performance
            if self.use_gpu:
                torch.cuda.empty_cache()
                # Reserve some GPU memory
                torch.cuda.set_per_process_memory_fraction(0.8)
            
            # Process in batches
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                
                # Generate embeddings for the batch
                # TODO: Replace with actual embedding model
                embeddings = np.random.randn(len(batch), self.dimension).astype('float32')
                
                # Extract document IDs and texts
                doc_ids = [doc['id'] for doc in batch]
                texts = [doc['text'] for doc in batch]
                
                if self.using_torch_fallback:
                    # Use TorchVectorStore's add method
                    self.index.add(embeddings, doc_ids, texts)
                else:
                    # Using FAISS
                    if self.use_gpu:
                        # Move embeddings to GPU if needed
                        gpu_embeddings = torch.from_numpy(embeddings).cuda()
                        embeddings = gpu_embeddings.cpu().numpy()
                    
                    # Add to FAISS index
                    self.index.add(embeddings)
                
                # Store document metadata in our local dictionary
                for doc in batch:
                    self.documents[doc['id']] = doc['text']
                
                # Optional: Print progress
                progress = (i + len(batch)) / len(documents) * 100
                print(f"Progress: {progress:.1f}% ({i + len(batch)}/{len(documents)} documents)")
            
            # Final optimization
            if self.use_gpu:
                self.optimize_index()
                torch.cuda.empty_cache()
            
            return True
            
        except Exception as e:
            print(f"Error in batch_add_documents: {str(e)}")
            return False
