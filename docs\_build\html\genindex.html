

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Index &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=39bd3b11" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=01f34227"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#_"><strong>_</strong></a>
 | <a href="#A"><strong>A</strong></a>
 | <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#L"><strong>L</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 | <a href="#V"><strong>V</strong></a>
 
</div>
<h2 id="_">_</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#models.ModelRole.__format__">__format__() (models.ModelRole method)</a>, <a href="modules.html#models.ModelRole.__format__">[1]</a>
</li>
      <li><a href="modules.html#core.cache.CacheManager.__init__">__init__() (core.cache.CacheManager method)</a>

      <ul>
        <li><a href="modules.html#core.cache.LRUCache.__init__">(core.cache.LRUCache method)</a>
</li>
        <li><a href="modules.html#exceptions.AuthenticationError.__init__">(exceptions.AuthenticationError method)</a>
</li>
        <li><a href="modules.html#exceptions.AuthorizationError.__init__">(exceptions.AuthorizationError method)</a>
</li>
        <li><a href="modules.html#exceptions.ConfigurationError.__init__">(exceptions.ConfigurationError method)</a>
</li>
        <li><a href="modules.html#exceptions.RateLimitError.__init__">(exceptions.RateLimitError method)</a>
</li>
        <li><a href="modules.html#exceptions.ReasoningError.__init__">(exceptions.ReasoningError method)</a>
</li>
        <li><a href="modules.html#exceptions.ResourceNotFoundError.__init__">(exceptions.ResourceNotFoundError method)</a>
</li>
        <li><a href="modules.html#exceptions.RetrievalError.__init__">(exceptions.RetrievalError method)</a>
</li>
        <li><a href="modules.html#exceptions.ServiceUnavailableError.__init__">(exceptions.ServiceUnavailableError method)</a>
</li>
        <li><a href="modules.html#exceptions.SymbolicAIException.__init__">(exceptions.SymbolicAIException method)</a>
</li>
        <li><a href="modules.html#exceptions.ValidationError.__init__">(exceptions.ValidationError method)</a>
</li>
        <li><a href="modules.html#exceptions.VectorStoreError.__init__">(exceptions.VectorStoreError method)</a>
</li>
        <li><a href="modules.html#logging_config.ContextFilter.__init__">(logging_config.ContextFilter method)</a>
</li>
        <li><a href="modules.html#logging_config.PerformanceFilter.__init__">(logging_config.PerformanceFilter method)</a>
</li>
        <li><a href="modules.html#logging_config.StructuredFormatter.__init__">(logging_config.StructuredFormatter method)</a>
</li>
        <li><a href="api/main.html#main.ResponseCache.__init__">(main.ResponseCache method)</a>, <a href="modules.html#main.ResponseCache.__init__">[1]</a>
</li>
        <li><a href="modules.html#monitoring.PerformanceMonitor.__init__">(monitoring.PerformanceMonitor method)</a>
</li>
        <li><a href="modules.html#monitoring.RequestMetrics.__init__">(monitoring.RequestMetrics method)</a>
</li>
        <li><a href="modules.html#monitoring.SystemMetrics.__init__">(monitoring.SystemMetrics method)</a>
</li>
        <li><a href="api/retrieval.html#id14">(retrieval.Retriever method)</a>, <a href="api/retrieval.html#id7">[1]</a>, <a href="api/retrieval.html#retrieval.Retriever.__init__">[2]</a>, <a href="modules.html#retrieval.Retriever.__init__">[3]</a>
</li>
        <li><a href="api/security.html#id12">(security.RateLimiter method)</a>, <a href="api/security.html#security.RateLimiter.__init__">[1]</a>, <a href="modules.html#security.RateLimiter.__init__">[2]</a>
</li>
        <li><a href="api/security.html#id1">(security.SecurityManager method)</a>, <a href="api/security.html#id6">[1]</a>, <a href="api/security.html#security.SecurityManager.__init__">[2]</a>, <a href="modules.html#security.SecurityManager.__init__">[3]</a>
</li>
        <li><a href="api/symbolic_reasoning.html#id4">(symbolic_reasoning.SymbolicReasoner method)</a>, <a href="api/symbolic_reasoning.html#id8">[1]</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner.__init__">[2]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner.__init__">[3]</a>
</li>
        <li><a href="modules.html#vector_store.TorchVectorStore.__init__">(vector_store.TorchVectorStore method)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#monitoring.SystemMetrics.active_requests">active_requests (monitoring.SystemMetrics attribute)</a>
</li>
      <li><a href="modules.html#vector_store.TorchVectorStore.add">add() (vector_store.TorchVectorStore method)</a>
</li>
      <li><a href="api/main.html#main.add_document">add_document() (in module main)</a>, <a href="modules.html#main.add_document">[1]</a>
</li>
      <li><a href="api/retrieval.html#id15">add_documents() (retrieval.Retriever method)</a>, <a href="api/retrieval.html#id9">[1]</a>, <a href="api/retrieval.html#retrieval.Retriever.add_documents">[2]</a>, <a href="modules.html#retrieval.Retriever.add_documents">[3]</a>
</li>
      <li><a href="api/main.html#main.add_security_headers">add_security_headers() (in module main)</a>, <a href="modules.html#main.add_security_headers">[1]</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/main.html#main.api_info">api_info() (in module main)</a>, <a href="modules.html#main.api_info">[1]</a>
</li>
      <li><a href="api/models.html#models.ModelRole.ASSISTANT">ASSISTANT (models.ModelRole attribute)</a>, <a href="modules.html#models.ModelRole.ASSISTANT">[1]</a>
</li>
      <li><a href="api/main.html#main.authentication_exception_handler">authentication_exception_handler() (in module main)</a>, <a href="modules.html#main.authentication_exception_handler">[1]</a>
</li>
      <li><a href="modules.html#exceptions.AuthenticationError">AuthenticationError</a>
</li>
      <li><a href="modules.html#exceptions.AuthorizationError">AuthorizationError</a>
</li>
  </ul></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/retrieval.html#id13">batch_add_documents() (retrieval.Retriever method)</a>, <a href="api/retrieval.html#id16">[1]</a>, <a href="api/retrieval.html#retrieval.Retriever.batch_add_documents">[2]</a>, <a href="modules.html#retrieval.Retriever.batch_add_documents">[3]</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/symbolic_reasoning.html#id10">batch_process_queries() (symbolic_reasoning.SymbolicReasoner method)</a>, <a href="api/symbolic_reasoning.html#id7">[1]</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner.batch_process_queries">[2]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner.batch_process_queries">[3]</a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#id33">cache (models.PerformanceMetrics attribute)</a>, <a href="api/models.html#id49">[1]</a>, <a href="api/models.html#id53">[2]</a>, <a href="api/models.html#models.PerformanceMetrics.cache">[3]</a>, <a href="modules.html#id33">[4]</a>, <a href="modules.html#models.PerformanceMetrics.cache">[5]</a>
</li>
      <li><a href="modules.html#monitoring.SystemMetrics.cache_hits">cache_hits (monitoring.SystemMetrics attribute)</a>
</li>
      <li><a href="modules.html#monitoring.SystemMetrics.cache_misses">cache_misses (monitoring.SystemMetrics attribute)</a>
</li>
      <li><a href="modules.html#monitoring.SystemMetrics.cache_size">cache_size (monitoring.SystemMetrics attribute)</a>
</li>
      <li><a href="api/main.html#main.LegacyChatResponse.cached">cached (main.LegacyChatResponse attribute)</a>, <a href="modules.html#main.LegacyChatResponse.cached">[1]</a>

      <ul>
        <li><a href="modules.html#monitoring.RequestMetrics.cached">(monitoring.RequestMetrics attribute)</a>
</li>
      </ul></li>
      <li><a href="modules.html#core.cache.CacheManager">CacheManager (class in core.cache)</a>
</li>
      <li><a href="api/main.html#main.chat">chat() (in module main)</a>, <a href="modules.html#main.chat">[1]</a>
</li>
      <li><a href="api/main.html#main.chat_stream">chat_stream() (in module main)</a>, <a href="modules.html#main.chat_stream">[1]</a>
</li>
      <li><a href="api/models.html#models.ChatChoice">ChatChoice (class in models)</a>, <a href="modules.html#models.ChatChoice">[1]</a>
</li>
      <li><a href="api/models.html#models.ChatMessage">ChatMessage (class in models)</a>, <a href="modules.html#models.ChatMessage">[1]</a>
</li>
      <li><a href="api/models.html#models.ChatRequest">ChatRequest (class in models)</a>, <a href="modules.html#models.ChatRequest">[1]</a>
</li>
      <li><a href="api/models.html#models.ChatResponse">ChatResponse (class in models)</a>, <a href="modules.html#models.ChatResponse">[1]</a>
</li>
      <li><a href="api/security.html#id15">check_request_size() (in module security)</a>, <a href="api/security.html#security.check_request_size">[1]</a>, <a href="modules.html#security.check_request_size">[2]</a>
</li>
      <li><a href="api/main.html#main.check_request_size_middleware">check_request_size_middleware() (in module main)</a>, <a href="modules.html#main.check_request_size_middleware">[1]</a>
</li>
      <li><a href="api/models.html#id19">choices (models.ChatResponse attribute)</a>, <a href="api/models.html#models.ChatResponse.choices">[1]</a>, <a href="modules.html#id19">[2]</a>, <a href="modules.html#models.ChatResponse.choices">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatResponse.choices">(models.OpenAIChatResponse attribute)</a>, <a href="modules.html#models.OpenAIChatResponse.choices">[1]</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/main.html#main.ResponseCache.clean">clean() (main.ResponseCache method)</a>, <a href="modules.html#main.ResponseCache.clean">[1]</a>
</li>
      <li><a href="api/main.html#main.clean_cache">clean_cache() (in module main)</a>, <a href="modules.html#main.clean_cache">[1]</a>
</li>
      <li><a href="modules.html#core.cache.CacheManager.cleanup_all">cleanup_all() (core.cache.CacheManager method)</a>
</li>
      <li><a href="modules.html#core.cache.LRUCache.cleanup_expired">cleanup_expired() (core.cache.LRUCache method)</a>
</li>
      <li><a href="modules.html#core.cache.LRUCache.clear">clear() (core.cache.LRUCache method)</a>
</li>
      <li><a href="modules.html#logging_config.ContextFilter.clear_context">clear_context() (logging_config.ContextFilter method)</a>
</li>
      <li><a href="api/models.html#id13">completion_tokens (models.TokenUsage attribute)</a>, <a href="api/models.html#models.TokenUsage.completion_tokens">[1]</a>, <a href="modules.html#id13">[2]</a>, <a href="modules.html#models.TokenUsage.completion_tokens">[3]</a>
</li>
      <li><a href="modules.html#exceptions.ConfigurationError">ConfigurationError</a>
</li>
      <li><a href="api/models.html#id1">content (models.ChatMessage attribute)</a>, <a href="api/models.html#models.ChatMessage.content">[1]</a>, <a href="modules.html#id1">[2]</a>, <a href="modules.html#models.ChatMessage.content">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIMessage.content">(models.OpenAIMessage attribute)</a>, <a href="modules.html#models.OpenAIMessage.content">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#logging_config.ContextFilter">ContextFilter (class in logging_config)</a>
</li>
      <li>
    core.cache

      <ul>
        <li><a href="modules.html#module-core.cache">module</a>
</li>
      </ul></li>
      <li><a href="modules.html#monitoring.SystemMetrics.cpu_percent">cpu_percent (monitoring.SystemMetrics attribute)</a>
</li>
      <li><a href="modules.html#core.cache.CacheManager.create_cache">create_cache() (core.cache.CacheManager method)</a>
</li>
      <li><a href="api/models.html#id17">created (models.ChatResponse attribute)</a>, <a href="api/models.html#models.ChatResponse.created">[1]</a>, <a href="modules.html#id17">[2]</a>, <a href="modules.html#models.ChatResponse.created">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatResponse.created">(models.OpenAIChatResponse attribute)</a>, <a href="modules.html#models.OpenAIChatResponse.created">[1]</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#core.cache.LRUCache.delete">delete() (core.cache.LRUCache method)</a>
</li>
      <li><a href="modules.html#exceptions.SymbolicAIException.details">details (exceptions.SymbolicAIException attribute)</a>
</li>
      <li><a href="api/retrieval.html#id5">dimension (retrieval.Retriever attribute)</a>, <a href="api/retrieval.html#retrieval.Retriever.dimension">[1]</a>, <a href="modules.html#retrieval.Retriever.dimension">[2]</a>
</li>
      <li><a href="api/models.html#id23">document_id (models.DocumentAddRequest attribute)</a>, <a href="api/models.html#models.DocumentAddRequest.document_id">[1]</a>, <a href="modules.html#id23">[2]</a>, <a href="modules.html#models.DocumentAddRequest.document_id">[3]</a>

      <ul>
        <li><a href="api/models.html#id26">(models.DocumentAddResponse attribute)</a>, <a href="api/models.html#models.DocumentAddResponse.document_id">[1]</a>, <a href="modules.html#id26">[2]</a>, <a href="modules.html#models.DocumentAddResponse.document_id">[3]</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#models.DocumentAddRequest">DocumentAddRequest (class in models)</a>, <a href="modules.html#models.DocumentAddRequest">[1]</a>
</li>
      <li><a href="api/models.html#models.DocumentAddResponse">DocumentAddResponse (class in models)</a>, <a href="modules.html#models.DocumentAddResponse">[1]</a>
</li>
      <li><a href="api/retrieval.html#id4">documents (retrieval.Retriever attribute)</a>, <a href="api/retrieval.html#retrieval.Retriever.documents">[1]</a>, <a href="modules.html#retrieval.Retriever.documents">[2]</a>
</li>
      <li><a href="modules.html#monitoring.RequestMetrics.duration">duration() (monitoring.RequestMetrics method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#monitoring.PerformanceMonitor.end_request">end_request() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="modules.html#monitoring.RequestMetrics.end_time">end_time (monitoring.RequestMetrics attribute)</a>
</li>
      <li><a href="modules.html#monitoring.RequestMetrics.endpoint">endpoint (monitoring.RequestMetrics attribute)</a>
</li>
      <li><a href="api/symbolic_reasoning.html#id2">engine (symbolic_reasoning.SymbolicReasoner attribute)</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner.engine">[1]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner.engine">[2]</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#monitoring.RequestMetrics.error">error (monitoring.RequestMetrics attribute)</a>
</li>
      <li><a href="modules.html#exceptions.SymbolicAIException.error_code">error_code (exceptions.SymbolicAIException attribute)</a>
</li>
      <li>
    exceptions

      <ul>
        <li><a href="modules.html#module-exceptions">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#logging_config.ContextFilter.filter">filter() (logging_config.ContextFilter method)</a>

      <ul>
        <li><a href="modules.html#logging_config.PerformanceFilter.filter">(logging_config.PerformanceFilter method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#id11">finish_reason (models.ChatChoice attribute)</a>, <a href="api/models.html#models.ChatChoice.finish_reason">[1]</a>, <a href="modules.html#id11">[2]</a>, <a href="modules.html#models.ChatChoice.finish_reason">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatChoice.finish_reason">(models.OpenAIChatChoice attribute)</a>, <a href="modules.html#models.OpenAIChatChoice.finish_reason">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#logging_config.StructuredFormatter.format">format() (logging_config.StructuredFormatter method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/main.html#main.general_exception_handler">general_exception_handler() (in module main)</a>, <a href="modules.html#main.general_exception_handler">[1]</a>
</li>
      <li><a href="modules.html#core.cache.LRUCache.get">get() (core.cache.LRUCache method)</a>

      <ul>
        <li><a href="api/main.html#main.ResponseCache.get">(main.ResponseCache method)</a>, <a href="modules.html#main.ResponseCache.get">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#core.cache.CacheManager.get_all_stats">get_all_stats() (core.cache.CacheManager method)</a>
</li>
      <li><a href="modules.html#core.cache.CacheManager.get_cache">get_cache() (core.cache.CacheManager method)</a>

      <ul>
        <li><a href="modules.html#core.cache.get_cache">(in module core.cache)</a>
</li>
      </ul></li>
      <li><a href="api/security.html#id19">get_client_ip() (in module security)</a>, <a href="api/security.html#security.get_client_ip">[1]</a>, <a href="modules.html#security.get_client_ip">[2]</a>
</li>
      <li><a href="api/security.html#id16">get_cors_config() (in module security)</a>, <a href="api/security.html#security.get_cors_config">[1]</a>, <a href="modules.html#security.get_cors_config">[2]</a>
</li>
      <li><a href="api/main.html#main.get_document_count">get_document_count() (in module main)</a>, <a href="modules.html#main.get_document_count">[1]</a>
</li>
      <li><a href="modules.html#logging_config.get_logger">get_logger() (in module logging_config)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#monitoring.PerformanceMonitor.get_recent_metrics">get_recent_metrics() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="api/security.html#id18">get_security_headers() (in module security)</a>, <a href="api/security.html#security.get_security_headers">[1]</a>, <a href="modules.html#security.get_security_headers">[2]</a>
</li>
      <li><a href="api/retrieval.html#id11">get_system_info() (retrieval.Retriever method)</a>, <a href="api/retrieval.html#id18">[1]</a>, <a href="api/retrieval.html#retrieval.Retriever.get_system_info">[2]</a>, <a href="modules.html#retrieval.Retriever.get_system_info">[3]</a>

      <ul>
        <li><a href="api/symbolic_reasoning.html#id11">(symbolic_reasoning.SymbolicReasoner method)</a>, <a href="api/symbolic_reasoning.html#id6">[1]</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner.get_system_info">[2]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner.get_system_info">[3]</a>
</li>
        <li><a href="modules.html#vector_store.TorchVectorStore.get_system_info">(vector_store.TorchVectorStore method)</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id28">gpu_available (models.SystemInfo attribute)</a>, <a href="api/models.html#id37">[1]</a>, <a href="api/models.html#id43">[2]</a>, <a href="api/models.html#models.SystemInfo.gpu_available">[3]</a>, <a href="modules.html#id28">[4]</a>, <a href="modules.html#models.SystemInfo.gpu_available">[5]</a>
</li>
      <li><a href="modules.html#monitoring.SystemMetrics.gpu_memory_used">gpu_memory_used (monitoring.SystemMetrics attribute)</a>
</li>
      <li><a href="api/models.html#id29">gpu_name (models.SystemInfo attribute)</a>, <a href="api/models.html#id38">[1]</a>, <a href="api/models.html#id44">[2]</a>, <a href="api/models.html#models.SystemInfo.gpu_name">[3]</a>, <a href="modules.html#id29">[4]</a>, <a href="modules.html#models.SystemInfo.gpu_name">[5]</a>
</li>
      <li><a href="api/models.html#id30">gpu_optimized (models.SystemInfo attribute)</a>, <a href="api/models.html#id39">[1]</a>, <a href="api/models.html#id45">[2]</a>, <a href="api/models.html#models.SystemInfo.gpu_optimized">[3]</a>, <a href="modules.html#id30">[4]</a>, <a href="modules.html#models.SystemInfo.gpu_optimized">[5]</a>
</li>
      <li><a href="modules.html#monitoring.SystemMetrics.gpu_utilization">gpu_utilization (monitoring.SystemMetrics attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#exceptions.handle_exception">handle_exception() (in module exceptions)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#id15">id (models.ChatResponse attribute)</a>, <a href="api/models.html#models.ChatResponse.id">[1]</a>, <a href="modules.html#id15">[2]</a>, <a href="modules.html#models.ChatResponse.id">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatResponse.id">(models.OpenAIChatResponse attribute)</a>, <a href="modules.html#models.OpenAIChatResponse.id">[1]</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id9">index (models.ChatChoice attribute)</a>, <a href="api/models.html#models.ChatChoice.index">[1]</a>, <a href="modules.html#id9">[2]</a>, <a href="modules.html#models.ChatChoice.index">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatChoice.index">(models.OpenAIChatChoice attribute)</a>, <a href="modules.html#models.OpenAIChatChoice.index">[1]</a>
</li>
        <li><a href="api/retrieval.html#id3">(retrieval.Retriever attribute)</a>, <a href="api/retrieval.html#retrieval.Retriever.index">[1]</a>, <a href="modules.html#retrieval.Retriever.index">[2]</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/security.html#id13">is_allowed() (security.RateLimiter method)</a>, <a href="api/security.html#security.RateLimiter.is_allowed">[1]</a>, <a href="modules.html#security.RateLimiter.is_allowed">[2]</a>
</li>
      <li><a href="api/security.html#id3">is_ip_blocked() (security.SecurityManager method)</a>, <a href="api/security.html#id8">[1]</a>, <a href="api/security.html#security.SecurityManager.is_ip_blocked">[2]</a>, <a href="modules.html#security.SecurityManager.is_ip_blocked">[3]</a>
</li>
  </ul></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/main.html#main.LegacyChatRequest">LegacyChatRequest (class in main)</a>, <a href="modules.html#main.LegacyChatRequest">[1]</a>
</li>
      <li><a href="api/main.html#main.LegacyChatResponse">LegacyChatResponse (class in main)</a>, <a href="modules.html#main.LegacyChatResponse">[1]</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    logging_config

      <ul>
        <li><a href="modules.html#module-logging_config">module</a>
</li>
      </ul></li>
      <li><a href="modules.html#core.cache.LRUCache">LRUCache (class in core.cache)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    main

      <ul>
        <li><a href="api/main.html#module-main">module</a>, <a href="modules.html#module-main">[1]</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id6">max_tokens (models.ChatRequest attribute)</a>, <a href="api/models.html#models.ChatRequest.max_tokens">[1]</a>, <a href="modules.html#id6">[2]</a>, <a href="modules.html#models.ChatRequest.max_tokens">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatRequest.max_tokens">(models.OpenAIChatRequest attribute)</a>, <a href="modules.html#models.OpenAIChatRequest.max_tokens">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#monitoring.SystemMetrics.memory_percent">memory_percent (monitoring.SystemMetrics attribute)</a>
</li>
      <li><a href="modules.html#exceptions.SymbolicAIException.message">message (exceptions.SymbolicAIException attribute)</a>

      <ul>
        <li><a href="api/models.html#id10">(models.ChatChoice attribute)</a>, <a href="api/models.html#models.ChatChoice.message">[1]</a>, <a href="modules.html#id10">[2]</a>, <a href="modules.html#models.ChatChoice.message">[3]</a>
</li>
        <li><a href="api/models.html#id25">(models.DocumentAddResponse attribute)</a>, <a href="api/models.html#models.DocumentAddResponse.message">[1]</a>, <a href="modules.html#id25">[2]</a>, <a href="modules.html#models.DocumentAddResponse.message">[3]</a>
</li>
        <li><a href="api/models.html#models.OpenAIChatChoice.message">(models.OpenAIChatChoice attribute)</a>, <a href="modules.html#models.OpenAIChatChoice.message">[1]</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id3">messages (models.ChatRequest attribute)</a>, <a href="api/models.html#models.ChatRequest.messages">[1]</a>, <a href="modules.html#id3">[2]</a>, <a href="modules.html#models.ChatRequest.messages">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatRequest.messages">(models.OpenAIChatRequest attribute)</a>, <a href="modules.html#models.OpenAIChatRequest.messages">[1]</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id22">metadata (models.DocumentAddRequest attribute)</a>, <a href="api/models.html#models.DocumentAddRequest.metadata">[1]</a>, <a href="modules.html#id22">[2]</a>, <a href="modules.html#models.DocumentAddRequest.metadata">[3]</a>

      <ul>
        <li><a href="api/models.html#id27">(models.DocumentAddResponse attribute)</a>, <a href="api/models.html#models.DocumentAddResponse.metadata">[1]</a>, <a href="modules.html#id27">[2]</a>, <a href="modules.html#models.DocumentAddResponse.metadata">[3]</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id4">model (models.ChatRequest attribute)</a>, <a href="api/models.html#models.ChatRequest.model">[1]</a>, <a href="modules.html#id4">[2]</a>, <a href="modules.html#models.ChatRequest.model">[3]</a>

      <ul>
        <li><a href="api/models.html#id18">(models.ChatResponse attribute)</a>, <a href="api/models.html#models.ChatResponse.model">[1]</a>, <a href="modules.html#id18">[2]</a>, <a href="modules.html#models.ChatResponse.model">[3]</a>
</li>
        <li><a href="api/models.html#models.OpenAIChatRequest.model">(models.OpenAIChatRequest attribute)</a>, <a href="modules.html#models.OpenAIChatRequest.model">[1]</a>
</li>
        <li><a href="api/models.html#models.OpenAIChatResponse.model">(models.OpenAIChatResponse attribute)</a>, <a href="modules.html#models.OpenAIChatResponse.model">[1]</a>
</li>
        <li><a href="api/symbolic_reasoning.html#id3">(symbolic_reasoning.SymbolicReasoner attribute)</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner.model">[1]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner.model">[2]</a>
</li>
      </ul></li>
      <li><a href="api/main.html#main.LegacyChatRequest.model_config">model_config (main.LegacyChatRequest attribute)</a>, <a href="modules.html#main.LegacyChatRequest.model_config">[1]</a>

      <ul>
        <li><a href="api/main.html#main.LegacyChatResponse.model_config">(main.LegacyChatResponse attribute)</a>, <a href="modules.html#main.LegacyChatResponse.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.ChatChoice.model_config">(models.ChatChoice attribute)</a>, <a href="modules.html#models.ChatChoice.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.ChatMessage.model_config">(models.ChatMessage attribute)</a>, <a href="modules.html#models.ChatMessage.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.ChatRequest.model_config">(models.ChatRequest attribute)</a>, <a href="modules.html#models.ChatRequest.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.ChatResponse.model_config">(models.ChatResponse attribute)</a>, <a href="modules.html#models.ChatResponse.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.DocumentAddRequest.model_config">(models.DocumentAddRequest attribute)</a>, <a href="modules.html#models.DocumentAddRequest.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.DocumentAddResponse.model_config">(models.DocumentAddResponse attribute)</a>, <a href="modules.html#models.DocumentAddResponse.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.OpenAIChatChoice.model_config">(models.OpenAIChatChoice attribute)</a>, <a href="modules.html#models.OpenAIChatChoice.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.OpenAIChatRequest.model_config">(models.OpenAIChatRequest attribute)</a>, <a href="modules.html#models.OpenAIChatRequest.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.OpenAIChatResponse.model_config">(models.OpenAIChatResponse attribute)</a>, <a href="modules.html#models.OpenAIChatResponse.model_config">[1]</a>
</li>
        <li><a href="api/models.html#models.OpenAIMessage.model_config">(models.OpenAIMessage attribute)</a>, <a href="modules.html#models.OpenAIMessage.model_config">[1]</a>
</li>
        <li><a href="api/models.html#id52">(models.PerformanceMetrics attribute)</a>, <a href="api/models.html#models.PerformanceMetrics.model_config">[1]</a>, <a href="modules.html#models.PerformanceMetrics.model_config">[2]</a>
</li>
        <li><a href="api/models.html#id42">(models.SystemInfo attribute)</a>, <a href="api/models.html#models.SystemInfo.model_config">[1]</a>, <a href="modules.html#models.SystemInfo.model_config">[2]</a>
</li>
        <li><a href="api/models.html#models.TokenUsage.model_config">(models.TokenUsage attribute)</a>, <a href="modules.html#models.TokenUsage.model_config">[1]</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#models.ModelRole">ModelRole (class in models)</a>, <a href="modules.html#models.ModelRole">[1]</a>
</li>
      <li>
    models

      <ul>
        <li><a href="api/models.html#module-models">module</a>, <a href="modules.html#module-models">[1]</a>
</li>
      </ul></li>
      <li>
    module

      <ul>
        <li><a href="modules.html#module-core.cache">core.cache</a>
</li>
        <li><a href="modules.html#module-exceptions">exceptions</a>
</li>
        <li><a href="modules.html#module-logging_config">logging_config</a>
</li>
        <li><a href="api/main.html#module-main">main</a>, <a href="modules.html#module-main">[1]</a>
</li>
        <li><a href="api/models.html#module-models">models</a>, <a href="modules.html#module-models">[1]</a>
</li>
        <li><a href="modules.html#module-monitoring">monitoring</a>
</li>
        <li><a href="api/retrieval.html#module-retrieval">retrieval</a>, <a href="modules.html#module-retrieval">[1]</a>
</li>
        <li><a href="api/security.html#module-security">security</a>, <a href="modules.html#module-security">[1]</a>
</li>
        <li><a href="api/symbolic_reasoning.html#module-symbolic_reasoning">symbolic_reasoning</a>, <a href="modules.html#module-symbolic_reasoning">[1]</a>
</li>
        <li><a href="modules.html#module-vector_store">vector_store</a>
</li>
      </ul></li>
      <li>
    monitoring

      <ul>
        <li><a href="modules.html#module-monitoring">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#id16">object (models.ChatResponse attribute)</a>, <a href="api/models.html#models.ChatResponse.object">[1]</a>, <a href="modules.html#id16">[2]</a>, <a href="modules.html#models.ChatResponse.object">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatResponse.object">(models.OpenAIChatResponse attribute)</a>, <a href="modules.html#models.OpenAIChatResponse.object">[1]</a>
</li>
      </ul></li>
      <li><a href="api/main.html#main.openai_chat">openai_chat() (in module main)</a>, <a href="modules.html#main.openai_chat">[1]</a>
</li>
      <li><a href="api/main.html#main.openai_chat_stream">openai_chat_stream() (in module main)</a>, <a href="modules.html#main.openai_chat_stream">[1]</a>
</li>
      <li><a href="api/models.html#models.OpenAIChatChoice">OpenAIChatChoice (class in models)</a>, <a href="modules.html#models.OpenAIChatChoice">[1]</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#models.OpenAIChatRequest">OpenAIChatRequest (class in models)</a>, <a href="modules.html#models.OpenAIChatRequest">[1]</a>
</li>
      <li><a href="api/models.html#models.OpenAIChatResponse">OpenAIChatResponse (class in models)</a>, <a href="modules.html#models.OpenAIChatResponse">[1]</a>
</li>
      <li><a href="api/models.html#models.OpenAIMessage">OpenAIMessage (class in models)</a>, <a href="modules.html#models.OpenAIMessage">[1]</a>
</li>
      <li><a href="api/main.html#main.optimize_gpu_memory">optimize_gpu_memory() (in module main)</a>, <a href="modules.html#main.optimize_gpu_memory">[1]</a>
</li>
      <li><a href="api/retrieval.html#id12">optimize_index() (retrieval.Retriever method)</a>, <a href="api/retrieval.html#retrieval.Retriever.optimize_index">[1]</a>, <a href="modules.html#retrieval.Retriever.optimize_index">[2]</a>
</li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/main.html#main.performance_stats">performance_stats() (in module main)</a>, <a href="modules.html#main.performance_stats">[1]</a>
</li>
      <li><a href="modules.html#logging_config.PerformanceFilter">PerformanceFilter (class in logging_config)</a>
</li>
      <li><a href="api/models.html#id48">PerformanceMetrics (class in models)</a>, <a href="api/models.html#models.PerformanceMetrics">[1]</a>, <a href="modules.html#models.PerformanceMetrics">[2]</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#monitoring.PerformanceMonitor">PerformanceMonitor (class in monitoring)</a>
</li>
      <li><a href="api/main.html#main.process_query">process_query() (in module main)</a>, <a href="modules.html#main.process_query">[1]</a>

      <ul>
        <li><a href="api/symbolic_reasoning.html#id5">(symbolic_reasoning.SymbolicReasoner method)</a>, <a href="api/symbolic_reasoning.html#id9">[1]</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner.process_query">[2]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner.process_query">[3]</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id12">prompt_tokens (models.TokenUsage attribute)</a>, <a href="api/models.html#models.TokenUsage.prompt_tokens">[1]</a>, <a href="modules.html#id12">[2]</a>, <a href="modules.html#models.TokenUsage.prompt_tokens">[3]</a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/main.html#main.rate_limit_middleware">rate_limit_middleware() (in module main)</a>, <a href="modules.html#main.rate_limit_middleware">[1]</a>
</li>
      <li><a href="api/security.html#id11">RateLimiter (class in security)</a>, <a href="api/security.html#security.RateLimiter">[1]</a>, <a href="modules.html#security.RateLimiter">[2]</a>
</li>
      <li><a href="modules.html#exceptions.RateLimitError">RateLimitError</a>
</li>
      <li><a href="api/main.html#main.read_root">read_root() (in module main)</a>, <a href="modules.html#main.read_root">[1]</a>
</li>
      <li><a href="api/models.html#id31">reasoner_info (models.SystemInfo attribute)</a>, <a href="api/models.html#id40">[1]</a>, <a href="api/models.html#id46">[2]</a>, <a href="api/models.html#models.SystemInfo.reasoner_info">[3]</a>, <a href="modules.html#id31">[4]</a>, <a href="modules.html#models.SystemInfo.reasoner_info">[5]</a>
</li>
      <li><a href="modules.html#monitoring.RequestMetrics.reasoning_time">reasoning_time (monitoring.RequestMetrics attribute)</a>
</li>
      <li><a href="modules.html#exceptions.ReasoningError">ReasoningError</a>
</li>
      <li><a href="modules.html#monitoring.PerformanceMonitor.record_cache_hit">record_cache_hit() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="modules.html#monitoring.PerformanceMonitor.record_cache_miss">record_cache_miss() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="api/security.html#id4">record_failed_attempt() (security.SecurityManager method)</a>, <a href="api/security.html#id9">[1]</a>, <a href="api/security.html#security.SecurityManager.record_failed_attempt">[2]</a>, <a href="modules.html#security.SecurityManager.record_failed_attempt">[3]</a>
</li>
      <li><a href="modules.html#monitoring.PerformanceMonitor.record_reasoning_time">record_reasoning_time() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="modules.html#monitoring.PerformanceMonitor.record_retrieval_time">record_retrieval_time() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="modules.html#monitoring.PerformanceMonitor.record_token_count">record_token_count() (monitoring.PerformanceMonitor method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#monitoring.RequestMetrics.request_id">request_id (monitoring.RequestMetrics attribute)</a>
</li>
      <li><a href="modules.html#monitoring.RequestMetrics">RequestMetrics (class in monitoring)</a>
</li>
      <li><a href="api/models.html#id35">requests (models.PerformanceMetrics attribute)</a>, <a href="api/models.html#id51">[1]</a>, <a href="api/models.html#id55">[2]</a>, <a href="api/models.html#models.PerformanceMetrics.requests">[3]</a>, <a href="modules.html#id35">[4]</a>, <a href="modules.html#models.PerformanceMetrics.requests">[5]</a>
</li>
      <li><a href="modules.html#exceptions.ResourceNotFoundError">ResourceNotFoundError</a>
</li>
      <li><a href="api/main.html#main.LegacyChatResponse.response">response (main.LegacyChatResponse attribute)</a>, <a href="modules.html#main.LegacyChatResponse.response">[1]</a>
</li>
      <li><a href="api/main.html#main.ResponseCache">ResponseCache (class in main)</a>, <a href="modules.html#main.ResponseCache">[1]</a>
</li>
      <li>
    retrieval

      <ul>
        <li><a href="api/retrieval.html#module-retrieval">module</a>, <a href="modules.html#module-retrieval">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#monitoring.RequestMetrics.retrieval_time">retrieval_time (monitoring.RequestMetrics attribute)</a>
</li>
      <li><a href="modules.html#exceptions.RetrievalError">RetrievalError</a>
</li>
      <li><a href="api/retrieval.html#id0">Retriever (class in retrieval)</a>, <a href="api/retrieval.html#retrieval.Retriever">[1]</a>, <a href="modules.html#retrieval.Retriever">[2]</a>
</li>
      <li><a href="api/models.html#id32">retriever_info (models.SystemInfo attribute)</a>, <a href="api/models.html#id41">[1]</a>, <a href="api/models.html#id47">[2]</a>, <a href="api/models.html#models.SystemInfo.retriever_info">[3]</a>, <a href="modules.html#id32">[4]</a>, <a href="modules.html#models.SystemInfo.retriever_info">[5]</a>
</li>
      <li><a href="api/models.html#id0">role (models.ChatMessage attribute)</a>, <a href="api/models.html#models.ChatMessage.role">[1]</a>, <a href="modules.html#id0">[2]</a>, <a href="modules.html#models.ChatMessage.role">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIMessage.role">(models.OpenAIMessage attribute)</a>, <a href="modules.html#models.OpenAIMessage.role">[1]</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/security.html#id14">sanitize_input() (in module security.SecurityManager)</a>

      <ul>
        <li><a href="api/security.html#id10">(security.SecurityManager method)</a>, <a href="api/security.html#id5">[1]</a>, <a href="api/security.html#security.SecurityManager.sanitize_input">[2]</a>, <a href="modules.html#security.SecurityManager.sanitize_input">[3]</a>
</li>
      </ul></li>
      <li><a href="api/retrieval.html#id10">search() (retrieval.Retriever method)</a>, <a href="api/retrieval.html#id17">[1]</a>, <a href="api/retrieval.html#retrieval.Retriever.search">[2]</a>, <a href="modules.html#retrieval.Retriever.search">[3]</a>

      <ul>
        <li><a href="modules.html#vector_store.TorchVectorStore.search">(vector_store.TorchVectorStore method)</a>
</li>
      </ul></li>
      <li>
    security

      <ul>
        <li><a href="api/security.html#module-security">module</a>, <a href="modules.html#module-security">[1]</a>
</li>
      </ul></li>
      <li><a href="api/security.html#id0">SecurityManager (class in security)</a>, <a href="api/security.html#security.SecurityManager">[1]</a>, <a href="modules.html#security.SecurityManager">[2]</a>
</li>
      <li><a href="modules.html#exceptions.ServiceUnavailableError">ServiceUnavailableError</a>
</li>
      <li><a href="modules.html#core.cache.LRUCache.set">set() (core.cache.LRUCache method)</a>

      <ul>
        <li><a href="api/main.html#main.ResponseCache.set">(main.ResponseCache method)</a>, <a href="modules.html#main.ResponseCache.set">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#logging_config.ContextFilter.set_context">set_context() (logging_config.ContextFilter method)</a>
</li>
      <li><a href="modules.html#logging_config.setup_logging">setup_logging() (in module logging_config)</a>
</li>
      <li><a href="modules.html#logging_config.setup_structured_logging">setup_structured_logging() (in module logging_config)</a>
</li>
      <li><a href="api/retrieval.html#id8">setup_vector_db() (retrieval.Retriever method)</a>, <a href="api/retrieval.html#retrieval.Retriever.setup_vector_db">[1]</a>, <a href="modules.html#retrieval.Retriever.setup_vector_db">[2]</a>
</li>
      <li><a href="modules.html#monitoring.PerformanceMonitor.shutdown">shutdown() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="api/main.html#main.shutdown_event">shutdown_event() (in module main)</a>, <a href="modules.html#main.shutdown_event">[1]</a>
</li>
      <li><a href="modules.html#core.cache.LRUCache.size">size() (core.cache.LRUCache method)</a>

      <ul>
        <li><a href="api/main.html#main.ResponseCache.size">(main.ResponseCache method)</a>, <a href="modules.html#main.ResponseCache.size">[1]</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#monitoring.PerformanceMonitor.start_request">start_request() (monitoring.PerformanceMonitor method)</a>
</li>
      <li><a href="modules.html#monitoring.RequestMetrics.start_time">start_time (monitoring.RequestMetrics attribute)</a>
</li>
      <li><a href="modules.html#core.cache.LRUCache.stats">stats() (core.cache.LRUCache method)</a>
</li>
      <li><a href="modules.html#exceptions.SymbolicAIException.status_code">status_code (exceptions.SymbolicAIException attribute)</a>
</li>
      <li><a href="api/models.html#id7">stream (models.ChatRequest attribute)</a>, <a href="api/models.html#models.ChatRequest.stream">[1]</a>, <a href="modules.html#id7">[2]</a>, <a href="modules.html#models.ChatRequest.stream">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatRequest.stream">(models.OpenAIChatRequest attribute)</a>, <a href="modules.html#models.OpenAIChatRequest.stream">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#logging_config.StructuredFormatter">StructuredFormatter (class in logging_config)</a>
</li>
      <li><a href="api/models.html#id24">success (models.DocumentAddResponse attribute)</a>, <a href="api/models.html#models.DocumentAddResponse.success">[1]</a>, <a href="modules.html#id24">[2]</a>, <a href="modules.html#models.DocumentAddResponse.success">[3]</a>
</li>
      <li><a href="api/main.html#main.symbolic_ai_exception_handler">symbolic_ai_exception_handler() (in module main)</a>, <a href="modules.html#main.symbolic_ai_exception_handler">[1]</a>
</li>
      <li>
    symbolic_reasoning

      <ul>
        <li><a href="api/symbolic_reasoning.html#module-symbolic_reasoning">module</a>, <a href="modules.html#module-symbolic_reasoning">[1]</a>
</li>
      </ul></li>
      <li><a href="modules.html#exceptions.SymbolicAIException">SymbolicAIException</a>
</li>
      <li><a href="api/symbolic_reasoning.html#id0">SymbolicReasoner (class in symbolic_reasoning)</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner">[1]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner">[2]</a>
</li>
      <li><a href="api/models.html#models.ModelRole.SYSTEM">SYSTEM (models.ModelRole attribute)</a>, <a href="modules.html#models.ModelRole.SYSTEM">[1]</a>
</li>
      <li><a href="api/models.html#id34">system (models.PerformanceMetrics attribute)</a>, <a href="api/models.html#id50">[1]</a>, <a href="api/models.html#id54">[2]</a>, <a href="api/models.html#models.PerformanceMetrics.system">[3]</a>, <a href="modules.html#id34">[4]</a>, <a href="modules.html#models.PerformanceMetrics.system">[5]</a>
</li>
      <li><a href="api/main.html#main.system_info">system_info() (in module main)</a>, <a href="modules.html#main.system_info">[1]</a>
</li>
      <li><a href="api/models.html#id36">SystemInfo (class in models)</a>, <a href="api/models.html#models.SystemInfo">[1]</a>, <a href="modules.html#models.SystemInfo">[2]</a>
</li>
      <li><a href="modules.html#monitoring.SystemMetrics">SystemMetrics (class in monitoring)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#id5">temperature (models.ChatRequest attribute)</a>, <a href="api/models.html#models.ChatRequest.temperature">[1]</a>, <a href="modules.html#id5">[2]</a>, <a href="modules.html#models.ChatRequest.temperature">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatRequest.temperature">(models.OpenAIChatRequest attribute)</a>, <a href="modules.html#models.OpenAIChatRequest.temperature">[1]</a>
</li>
      </ul></li>
      <li><a href="api/main.html#main.LegacyChatRequest.text">text (main.LegacyChatRequest attribute)</a>, <a href="modules.html#main.LegacyChatRequest.text">[1]</a>

      <ul>
        <li><a href="api/models.html#id21">(models.DocumentAddRequest attribute)</a>, <a href="api/models.html#models.DocumentAddRequest.text">[1]</a>, <a href="modules.html#id21">[2]</a>, <a href="modules.html#models.DocumentAddRequest.text">[3]</a>
</li>
      </ul></li>
      <li><a href="api/models.html#id2">timestamp (models.ChatMessage attribute)</a>, <a href="api/models.html#models.ChatMessage.timestamp">[1]</a>, <a href="modules.html#id2">[2]</a>, <a href="modules.html#models.ChatMessage.timestamp">[3]</a>

      <ul>
        <li><a href="modules.html#monitoring.SystemMetrics.timestamp">(monitoring.SystemMetrics attribute)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="modules.html#exceptions.SymbolicAIException.to_dict">to_dict() (exceptions.SymbolicAIException method)</a>
</li>
      <li><a href="api/models.html#models.TokenUsage">TokenUsage (class in models)</a>, <a href="modules.html#models.TokenUsage">[1]</a>
</li>
      <li><a href="api/models.html#id8">top_p (models.ChatRequest attribute)</a>, <a href="api/models.html#models.ChatRequest.top_p">[1]</a>, <a href="modules.html#id8">[2]</a>, <a href="modules.html#models.ChatRequest.top_p">[3]</a>
</li>
      <li><a href="modules.html#vector_store.TorchVectorStore">TorchVectorStore (class in vector_store)</a>
</li>
      <li><a href="api/models.html#id14">total_tokens (models.TokenUsage attribute)</a>, <a href="api/models.html#models.TokenUsage.total_tokens">[1]</a>, <a href="modules.html#id14">[2]</a>, <a href="modules.html#models.TokenUsage.total_tokens">[3]</a>

      <ul>
        <li><a href="modules.html#monitoring.RequestMetrics.total_tokens">(monitoring.RequestMetrics attribute)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#id20">usage (models.ChatResponse attribute)</a>, <a href="api/models.html#models.ChatResponse.usage">[1]</a>, <a href="modules.html#id20">[2]</a>, <a href="modules.html#models.ChatResponse.usage">[3]</a>

      <ul>
        <li><a href="api/models.html#models.OpenAIChatResponse.usage">(models.OpenAIChatResponse attribute)</a>, <a href="modules.html#models.OpenAIChatResponse.usage">[1]</a>
</li>
      </ul></li>
      <li><a href="api/retrieval.html#id1">use_gpu (retrieval.Retriever attribute)</a>, <a href="api/retrieval.html#retrieval.Retriever.use_gpu">[1]</a>, <a href="modules.html#retrieval.Retriever.use_gpu">[2]</a>

      <ul>
        <li><a href="api/symbolic_reasoning.html#id1">(symbolic_reasoning.SymbolicReasoner attribute)</a>, <a href="api/symbolic_reasoning.html#symbolic_reasoning.SymbolicReasoner.use_gpu">[1]</a>, <a href="modules.html#symbolic_reasoning.SymbolicReasoner.use_gpu">[2]</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/models.html#models.ModelRole.USER">USER (models.ModelRole attribute)</a>, <a href="modules.html#models.ModelRole.USER">[1]</a>
</li>
      <li><a href="api/retrieval.html#id6">using_torch_fallback (retrieval.Retriever attribute)</a>, <a href="api/retrieval.html#retrieval.Retriever.using_torch_fallback">[1]</a>, <a href="modules.html#retrieval.Retriever.using_torch_fallback">[2]</a>
</li>
  </ul></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/security.html#id2">validate_api_key() (security.SecurityManager method)</a>, <a href="api/security.html#id7">[1]</a>, <a href="api/security.html#security.SecurityManager.validate_api_key">[2]</a>, <a href="modules.html#security.SecurityManager.validate_api_key">[3]</a>
</li>
      <li><a href="api/models.html#models.ChatMessage.validate_content">validate_content() (models.ChatMessage class method)</a>, <a href="modules.html#models.ChatMessage.validate_content">[1]</a>
</li>
      <li><a href="api/security.html#id17">validate_cors_origin() (in module security)</a>, <a href="api/security.html#security.validate_cors_origin">[1]</a>, <a href="modules.html#security.validate_cors_origin">[2]</a>
</li>
      <li><a href="api/models.html#models.ChatRequest.validate_messages">validate_messages() (models.ChatRequest class method)</a>, <a href="modules.html#models.ChatRequest.validate_messages">[1]</a>
</li>
      <li><a href="api/models.html#models.DocumentAddRequest.validate_text">validate_text() (models.DocumentAddRequest class method)</a>, <a href="modules.html#models.DocumentAddRequest.validate_text">[1]</a>
</li>
      <li><a href="api/models.html#models.TokenUsage.validate_total_tokens">validate_total_tokens() (models.TokenUsage class method)</a>, <a href="modules.html#models.TokenUsage.validate_total_tokens">[1]</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/main.html#main.validation_exception_handler">validation_exception_handler() (in module main)</a>, <a href="modules.html#main.validation_exception_handler">[1]</a>
</li>
      <li><a href="modules.html#exceptions.ValidationError">ValidationError</a>
</li>
      <li><a href="api/retrieval.html#id2">vector_db (retrieval.Retriever attribute)</a>, <a href="api/retrieval.html#retrieval.Retriever.vector_db">[1]</a>, <a href="modules.html#retrieval.Retriever.vector_db">[2]</a>
</li>
      <li>
    vector_store

      <ul>
        <li><a href="modules.html#module-vector_store">module</a>
</li>
      </ul></li>
      <li><a href="modules.html#exceptions.VectorStoreError">VectorStoreError</a>
</li>
      <li><a href="api/security.html#security.verify_api_key">verify_api_key() (in module security)</a>, <a href="modules.html#security.verify_api_key">[1]</a>
</li>
  </ul></td>
</tr></table>



           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>