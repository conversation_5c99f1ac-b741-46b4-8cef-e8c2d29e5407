Search.setIndex({"alltitles": {"API Key Authentication": [[3, "api-key-authentication"]], "API Key Management": [[3, "api-key-management"]], "API Reference": [[5, null], [7, null], [8, null]], "API Routes": [[8, "api-routes"]], "Advanced Configuration": [[4, "advanced-configuration"]], "Advanced Search": [[2, "advanced-search"]], "Anthropic Engine": [[4, "anthropic-engine"]], "Architecture": [[7, "architecture"]], "Authentication": [[3, "authentication"], [5, "authentication"]], "Base URL": [[5, "base-url"]], "Basic Document Retrieval": [[2, "basic-document-retrieval"]], "Basic Usage": [[4, "basic-usage"]], "Batch Processing": [[2, "batch-processing"], [2, "id19"], [4, "batch-processing"], [4, "id12"]], "Best Practices": [[1, "best-practices"], [2, "best-practices"], [3, "best-practices"], [6, "best-practices"]], "Built-in Validation": [[6, "built-in-validation"]], "CORS Configuration": [[3, "cors-configuration"], [3, "id22"]], "CORS Support": [[5, "cors-support"]], "Cache Configuration": [[6, "cache-configuration"]], "Caching": [[2, "caching"], [4, "caching"]], "Chat Completions": [[0, "chat-completions"]], "Chat Completions API": [[5, "chat-completions-api"]], "Chat Endpoints": [[8, "chat-endpoints"]], "ChatCompletionChunk": [[1, "chatcompletionchunk"]], "ChatCompletionRequest": [[1, "chatcompletionrequest"]], "ChatCompletionResponse": [[1, "chatcompletionresponse"]], "Choice": [[1, "choice"]], "ChoiceDelta": [[1, "choicedelta"]], "ChromaDB Backend": [[2, "chromadb-backend"]], "Client IP Detection": [[3, "client-ip-detection"]], "Common Issues": [[6, "common-issues"]], "Common Parameters": [[5, "common-parameters"]], "Complete Request/Response Cycle": [[1, "complete-request-response-cycle"]], "Complete Security Setup": [[3, "complete-security-setup"]], "Configuration": [[0, "configuration"], [2, "configuration"], [4, "configuration"]], "Configuration Files": [[6, "configuration-files"]], "Configuration Guide": [[6, null]], "Configuration Management": [[8, "module-core.cache"]], "Configuration Templates": [[6, "configuration-templates"]], "Configuration Validation": [[6, "configuration-validation"]], "Core Application Settings": [[6, "core-application-settings"]], "Core Components": [[7, "core-components"], [8, "module-main"]], "Core Methods": [[2, "core-methods"], [3, "core-methods"], [4, "core-methods"]], "Core Modules": [[8, "core-modules"]], "Custom Validation": [[6, "custom-validation"]], "Custom Validators": [[1, "custom-validators"]], "Data Models Module": [[1, null]], "Default Limits": [[5, "default-limits"]], "Developer Guide": [[7, null]], "Development Environment": [[6, "development-environment"]], "Dictionary Conversion": [[1, "dictionary-conversion"]], "Docker Configuration": [[6, "docker-configuration"]], "Document Preparation": [[2, "document-preparation"]], "Embedding Models": [[2, "embedding-models"]], "Endpoints": [[0, "endpoints"]], "Environment Management": [[6, "environment-management"]], "Environment Setup": [[6, "environment-setup"]], "Environment Variables": [[6, "environment-variables"]], "Environment-Specific YAML Files": [[6, "environment-specific-yaml-files"]], "Error Codes": [[5, "error-codes"]], "Error Format": [[5, "error-format"]], "Error Handling": [[0, "error-handling"], [2, "error-handling"], [3, "error-handling"], [4, "error-handling"]], "Error Models": [[1, "error-models"]], "Error Responses": [[5, "error-responses"]], "ErrorDetail": [[1, "errordetail"]], "ErrorResponse": [[1, "errorresponse"]], "Example Usage": [[0, "example-usage"], [1, "example-usage"], [2, "example-usage"], [3, "example-usage"], [4, "example-usage"]], "Exception Handling": [[8, "module-exceptions"]], "FAISS Backend": [[2, "faiss-backend"]], "Failed Attempt Tracking": [[3, "failed-attempt-tracking"]], "FastAPI Application": [[0, "fastapi-application"]], "Features": [[7, "features"]], "Field Validation": [[1, "field-validation"]], "GPU Acceleration": [[2, "gpu-acceleration"]], "GPU Usage": [[4, "gpu-usage"]], "Generate Environment Template": [[6, "generate-environment-template"]], "Health Check": [[5, "health-check"]], "Health Checks": [[0, "health-checks"]], "IP Blocking": [[3, "ip-blocking"]], "Index Optimization": [[2, "index-optimization"]], "Indices and Tables": [[7, "indices-and-tables"]], "Initialization": [[2, "initialization"], [3, "initialization"], [4, "initialization"]], "Input Sanitization": [[3, "input-sanitization"]], "Input Validation": [[3, "input-validation"], [3, "id21"]], "JSON Serialization": [[1, "json-serialization"]], "License": [[7, "license"]], "Loading Configuration Files": [[6, "loading-configuration-files"]], "Local Engine": [[4, "local-engine"]], "Logging Configuration": [[6, "logging-configuration"]], "Main Application Module": [[0, null]], "Message": [[1, "message"]], "Message Format": [[5, "message-format"]], "Model Configuration": [[6, "model-configuration"]], "Model Design": [[1, "model-design"]], "Monitoring": [[6, "monitoring"]], "Monitoring & Observability": [[7, "monitoring-observability"]], "Monitoring Endpoints": [[8, "monitoring-endpoints"]], "Monitoring and Logging": [[8, "module-monitoring"]], "Neural Symbolic Language Model Documentation": [[7, null]], "OpenAI Engine": [[4, "openai-engine"]], "OpenAI-Compatible Endpoint": [[5, "openai-compatible-endpoint"]], "OpenAPI Documentation": [[5, "openapi-documentation"]], "Origin Validation": [[3, "origin-validation"]], "Overview": [[1, "overview"], [2, "overview"], [3, "overview"], [4, "overview"], [6, "overview"], [7, "overview"]], "Performance": [[1, "performance"], [6, "performance"]], "Performance Considerations": [[4, "performance-considerations"]], "Performance Features": [[7, "performance-features"]], "Performance Metrics": [[5, "performance-metrics"]], "Performance Monitoring": [[0, "performance-monitoring"]], "Performance Monitoring API": [[5, "performance-monitoring-api"]], "Performance Optimization": [[2, "performance-optimization"]], "PerformanceMetrics": [[1, "performancemetrics"]], "Production Deployment": [[7, "production-deployment"]], "Production Environment": [[6, "production-environment"]], "Query Optimization": [[2, "query-optimization"]], "Quick Start": [[7, "quick-start"]], "REST API Endpoints": [[5, "rest-api-endpoints"]], "Rate Limit Headers": [[5, "rate-limit-headers"]], "Rate Limiting": [[3, "rate-limiting"], [3, "id20"], [5, "rate-limiting"]], "RateLimiter Class": [[3, "ratelimiter-class"]], "Readiness Check": [[5, "readiness-check"]], "Request Models": [[1, "request-models"]], "Request Parameters": [[5, "request-parameters"]], "Request Size Limits": [[3, "request-size-limits"]], "Response Models": [[1, "response-models"]], "Retriever Class": [[2, "retriever-class"]], "Scaling Considerations": [[2, "scaling-considerations"]], "Security": [[0, "security"], [6, "security"]], "Security Configuration": [[6, "security-configuration"]], "Security Features": [[7, "security-features"]], "Security Headers": [[3, "security-headers"]], "Security Module": [[3, null]], "SecurityManager Class": [[3, "securitymanager-class"]], "Serialization": [[1, "serialization"]], "Streaming Models": [[1, "streaming-models"]], "Streaming Responses": [[5, "streaming-responses"]], "Support": [[7, "support"]], "Supported Backends": [[2, "supported-backends"]], "Supported Engines": [[4, "supported-engines"]], "Symbolic Reasoning Module": [[4, null]], "SymbolicReasoner Class": [[4, "symbolicreasoner-class"]], "System Endpoints": [[8, "system-endpoints"]], "System Info": [[5, "system-info"]], "System Information": [[0, "system-information"], [2, "system-information"]], "System Information API": [[5, "system-information-api"]], "System Models": [[1, "system-models"]], "SystemInfo": [[1, "systeminfo"]], "Table of Contents": [[7, "table-of-contents"]], "Troubleshooting": [[6, "troubleshooting"]], "Usage": [[1, "usage"]], "User Guide": [[7, null]], "Utility Modules": [[8, "utility-modules"]], "Validation": [[1, "validation"]], "Validation Features": [[1, "validation-features"]], "Vector Retrieval Module": [[2, null]], "Vector Storage": [[8, "module-vector_store"]]}, "docnames": ["api/main", "api/models", "api/retrieval", "api/security", "api/symbolic_reasoning", "api_reference", "configuration", "index", "modules"], "envversion": {"sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["api\\main.rst", "api\\models.rst", "api\\retrieval.rst", "api\\security.rst", "api\\symbolic_reasoning.rst", "api_reference.rst", "configuration.rst", "index.rst", "modules.rst"], "indexentries": {"__format__() (models.modelrole method)": [[1, "models.ModelRole.__format__", false], [8, "models.ModelRole.__format__", false]], "__init__() (core.cache.cachemanager method)": [[8, "core.cache.CacheManager.__init__", false]], "__init__() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.__init__", false]], "__init__() (exceptions.authenticationerror method)": [[8, "exceptions.AuthenticationError.__init__", false]], "__init__() (exceptions.authorizationerror method)": [[8, "exceptions.AuthorizationError.__init__", false]], "__init__() (exceptions.configurationerror method)": [[8, "exceptions.ConfigurationError.__init__", false]], "__init__() (exceptions.ratelimiterror method)": [[8, "exceptions.RateLimitError.__init__", false]], "__init__() (exceptions.reasoningerror method)": [[8, "exceptions.ReasoningError.__init__", false]], "__init__() (exceptions.resourcenotfounderror method)": [[8, "exceptions.ResourceNotFoundError.__init__", false]], "__init__() (exceptions.retrievalerror method)": [[8, "exceptions.RetrievalError.__init__", false]], "__init__() (exceptions.serviceunavailableerror method)": [[8, "exceptions.ServiceUnavailableError.__init__", false]], "__init__() (exceptions.symbolicaiexception method)": [[8, "exceptions.SymbolicAIException.__init__", false]], "__init__() (exceptions.validationerror method)": [[8, "exceptions.ValidationError.__init__", false]], "__init__() (exceptions.vectorstoreerror method)": [[8, "exceptions.VectorStoreError.__init__", false]], "__init__() (logging_config.contextfilter method)": [[8, "logging_config.ContextFilter.__init__", false]], "__init__() (logging_config.performancefilter method)": [[8, "logging_config.PerformanceFilter.__init__", false]], "__init__() (logging_config.structuredformatter method)": [[8, "logging_config.StructuredFormatter.__init__", false]], "__init__() (main.responsecache method)": [[0, "main.ResponseCache.__init__", false], [8, "main.ResponseCache.__init__", false]], "__init__() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.__init__", false]], "__init__() (monitoring.requestmetrics method)": [[8, "monitoring.RequestMetrics.__init__", false]], "__init__() (monitoring.systemmetrics method)": [[8, "monitoring.SystemMetrics.__init__", false]], "__init__() (retrieval.retriever method)": [[2, "id14", false], [2, "id7", false], [2, "retrieval.Retriever.__init__", false], [8, "retrieval.Retriever.__init__", false]], "__init__() (security.ratelimiter method)": [[3, "id12", false], [3, "security.RateLimiter.__init__", false], [8, "security.RateLimiter.__init__", false]], "__init__() (security.securitymanager method)": [[3, "id1", false], [3, "id6", false], [3, "security.SecurityManager.__init__", false], [8, "security.SecurityManager.__init__", false]], "__init__() (symbolic_reasoning.symbolicreasoner method)": [[4, "id4", false], [4, "id8", false], [4, "symbolic_reasoning.SymbolicReasoner.__init__", false], [8, "symbolic_reasoning.SymbolicReasoner.__init__", false]], "__init__() (vector_store.torchvectorstore method)": [[8, "vector_store.TorchVectorStore.__init__", false]], "active_requests (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.active_requests", false]], "add() (vector_store.torchvectorstore method)": [[8, "vector_store.TorchVectorStore.add", false]], "add_document() (in module main)": [[0, "main.add_document", false], [8, "main.add_document", false]], "add_documents() (retrieval.retriever method)": [[2, "id15", false], [2, "id9", false], [2, "retrieval.Retriever.add_documents", false], [8, "retrieval.Retriever.add_documents", false]], "add_security_headers() (in module main)": [[0, "main.add_security_headers", false], [8, "main.add_security_headers", false]], "api_info() (in module main)": [[0, "main.api_info", false], [8, "main.api_info", false]], "assistant (models.modelrole attribute)": [[1, "models.ModelRole.ASSISTANT", false], [8, "models.ModelRole.ASSISTANT", false]], "authentication_exception_handler() (in module main)": [[0, "main.authentication_exception_handler", false], [8, "main.authentication_exception_handler", false]], "authenticationerror": [[8, "exceptions.AuthenticationError", false]], "authorizationerror": [[8, "exceptions.AuthorizationError", false]], "batch_add_documents() (retrieval.retriever method)": [[2, "id13", false], [2, "id16", false], [2, "retrieval.Retriever.batch_add_documents", false], [8, "retrieval.Retriever.batch_add_documents", false]], "batch_process_queries() (symbolic_reasoning.symbolicreasoner method)": [[4, "id10", false], [4, "id7", false], [4, "symbolic_reasoning.SymbolicReasoner.batch_process_queries", false], [8, "symbolic_reasoning.SymbolicReasoner.batch_process_queries", false]], "cache (models.performancemetrics attribute)": [[1, "id33", false], [1, "id49", false], [1, "id53", false], [1, "models.PerformanceMetrics.cache", false], [8, "id33", false], [8, "models.PerformanceMetrics.cache", false]], "cache_hits (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.cache_hits", false]], "cache_misses (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.cache_misses", false]], "cache_size (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.cache_size", false]], "cached (main.legacychatresponse attribute)": [[0, "main.LegacyChatResponse.cached", false], [8, "main.LegacyChatResponse.cached", false]], "cached (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.cached", false]], "cachemanager (class in core.cache)": [[8, "core.cache.CacheManager", false]], "chat() (in module main)": [[0, "main.chat", false], [8, "main.chat", false]], "chat_stream() (in module main)": [[0, "main.chat_stream", false], [8, "main.chat_stream", false]], "chatchoice (class in models)": [[1, "models.ChatChoice", false], [8, "models.ChatChoice", false]], "chatmessage (class in models)": [[1, "models.ChatMessage", false], [8, "models.ChatMessage", false]], "chatrequest (class in models)": [[1, "models.ChatRequest", false], [8, "models.ChatRequest", false]], "chatresponse (class in models)": [[1, "models.ChatResponse", false], [8, "models.ChatResponse", false]], "check_request_size() (in module security)": [[3, "id15", false], [3, "security.check_request_size", false], [8, "security.check_request_size", false]], "check_request_size_middleware() (in module main)": [[0, "main.check_request_size_middleware", false], [8, "main.check_request_size_middleware", false]], "choices (models.chatresponse attribute)": [[1, "id19", false], [1, "models.ChatResponse.choices", false], [8, "id19", false], [8, "models.ChatResponse.choices", false]], "choices (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.choices", false], [8, "models.OpenAIChatResponse.choices", false]], "clean() (main.responsecache method)": [[0, "main.ResponseCache.clean", false], [8, "main.ResponseCache.clean", false]], "clean_cache() (in module main)": [[0, "main.clean_cache", false], [8, "main.clean_cache", false]], "cleanup_all() (core.cache.cachemanager method)": [[8, "core.cache.CacheManager.cleanup_all", false]], "cleanup_expired() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.cleanup_expired", false]], "clear() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.clear", false]], "clear_context() (logging_config.contextfilter method)": [[8, "logging_config.ContextFilter.clear_context", false]], "completion_tokens (models.tokenusage attribute)": [[1, "id13", false], [1, "models.TokenUsage.completion_tokens", false], [8, "id13", false], [8, "models.TokenUsage.completion_tokens", false]], "configurationerror": [[8, "exceptions.ConfigurationError", false]], "content (models.chatmessage attribute)": [[1, "id1", false], [1, "models.ChatMessage.content", false], [8, "id1", false], [8, "models.ChatMessage.content", false]], "content (models.openaimessage attribute)": [[1, "models.OpenAIMessage.content", false], [8, "models.OpenAIMessage.content", false]], "contextfilter (class in logging_config)": [[8, "logging_config.ContextFilter", false]], "core.cache": [[8, "module-core.cache", false]], "cpu_percent (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.cpu_percent", false]], "create_cache() (core.cache.cachemanager method)": [[8, "core.cache.CacheManager.create_cache", false]], "created (models.chatresponse attribute)": [[1, "id17", false], [1, "models.ChatResponse.created", false], [8, "id17", false], [8, "models.ChatResponse.created", false]], "created (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.created", false], [8, "models.OpenAIChatResponse.created", false]], "delete() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.delete", false]], "details (exceptions.symbolicaiexception attribute)": [[8, "exceptions.SymbolicAIException.details", false]], "dimension (retrieval.retriever attribute)": [[2, "id5", false], [2, "retrieval.Retriever.dimension", false], [8, "retrieval.Retriever.dimension", false]], "document_id (models.documentaddrequest attribute)": [[1, "id23", false], [1, "models.DocumentAddRequest.document_id", false], [8, "id23", false], [8, "models.DocumentAddRequest.document_id", false]], "document_id (models.documentaddresponse attribute)": [[1, "id26", false], [1, "models.DocumentAddResponse.document_id", false], [8, "id26", false], [8, "models.DocumentAddResponse.document_id", false]], "documentaddrequest (class in models)": [[1, "models.DocumentAddRequest", false], [8, "models.DocumentAddRequest", false]], "documentaddresponse (class in models)": [[1, "models.DocumentAddResponse", false], [8, "models.DocumentAddResponse", false]], "documents (retrieval.retriever attribute)": [[2, "id4", false], [2, "retrieval.Retriever.documents", false], [8, "retrieval.Retriever.documents", false]], "duration() (monitoring.requestmetrics method)": [[8, "monitoring.RequestMetrics.duration", false]], "end_request() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.end_request", false]], "end_time (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.end_time", false]], "endpoint (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.endpoint", false]], "engine (symbolic_reasoning.symbolicreasoner attribute)": [[4, "id2", false], [4, "symbolic_reasoning.SymbolicReasoner.engine", false], [8, "symbolic_reasoning.SymbolicReasoner.engine", false]], "error (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.error", false]], "error_code (exceptions.symbolicaiexception attribute)": [[8, "exceptions.SymbolicAIException.error_code", false]], "exceptions": [[8, "module-exceptions", false]], "filter() (logging_config.contextfilter method)": [[8, "logging_config.ContextFilter.filter", false]], "filter() (logging_config.performancefilter method)": [[8, "logging_config.PerformanceFilter.filter", false]], "finish_reason (models.chatchoice attribute)": [[1, "id11", false], [1, "models.ChatChoice.finish_reason", false], [8, "id11", false], [8, "models.ChatChoice.finish_reason", false]], "finish_reason (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.finish_reason", false], [8, "models.OpenAIChatChoice.finish_reason", false]], "format() (logging_config.structuredformatter method)": [[8, "logging_config.StructuredFormatter.format", false]], "general_exception_handler() (in module main)": [[0, "main.general_exception_handler", false], [8, "main.general_exception_handler", false]], "get() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.get", false]], "get() (main.responsecache method)": [[0, "main.ResponseCache.get", false], [8, "main.ResponseCache.get", false]], "get_all_stats() (core.cache.cachemanager method)": [[8, "core.cache.CacheManager.get_all_stats", false]], "get_cache() (core.cache.cachemanager method)": [[8, "core.cache.CacheManager.get_cache", false]], "get_cache() (in module core.cache)": [[8, "core.cache.get_cache", false]], "get_client_ip() (in module security)": [[3, "id19", false], [3, "security.get_client_ip", false], [8, "security.get_client_ip", false]], "get_cors_config() (in module security)": [[3, "id16", false], [3, "security.get_cors_config", false], [8, "security.get_cors_config", false]], "get_document_count() (in module main)": [[0, "main.get_document_count", false], [8, "main.get_document_count", false]], "get_logger() (in module logging_config)": [[8, "logging_config.get_logger", false]], "get_recent_metrics() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.get_recent_metrics", false]], "get_security_headers() (in module security)": [[3, "id18", false], [3, "security.get_security_headers", false], [8, "security.get_security_headers", false]], "get_system_info() (retrieval.retriever method)": [[2, "id11", false], [2, "id18", false], [2, "retrieval.Retriever.get_system_info", false], [8, "retrieval.Retriever.get_system_info", false]], "get_system_info() (symbolic_reasoning.symbolicreasoner method)": [[4, "id11", false], [4, "id6", false], [4, "symbolic_reasoning.SymbolicReasoner.get_system_info", false], [8, "symbolic_reasoning.SymbolicReasoner.get_system_info", false]], "get_system_info() (vector_store.torchvectorstore method)": [[8, "vector_store.TorchVectorStore.get_system_info", false]], "gpu_available (models.systeminfo attribute)": [[1, "id28", false], [1, "id37", false], [1, "id43", false], [1, "models.SystemInfo.gpu_available", false], [8, "id28", false], [8, "models.SystemInfo.gpu_available", false]], "gpu_memory_used (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.gpu_memory_used", false]], "gpu_name (models.systeminfo attribute)": [[1, "id29", false], [1, "id38", false], [1, "id44", false], [1, "models.SystemInfo.gpu_name", false], [8, "id29", false], [8, "models.SystemInfo.gpu_name", false]], "gpu_optimized (models.systeminfo attribute)": [[1, "id30", false], [1, "id39", false], [1, "id45", false], [1, "models.SystemInfo.gpu_optimized", false], [8, "id30", false], [8, "models.SystemInfo.gpu_optimized", false]], "gpu_utilization (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.gpu_utilization", false]], "handle_exception() (in module exceptions)": [[8, "exceptions.handle_exception", false]], "id (models.chatresponse attribute)": [[1, "id15", false], [1, "models.ChatResponse.id", false], [8, "id15", false], [8, "models.ChatResponse.id", false]], "id (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.id", false], [8, "models.OpenAIChatResponse.id", false]], "index (models.chatchoice attribute)": [[1, "id9", false], [1, "models.ChatChoice.index", false], [8, "id9", false], [8, "models.ChatChoice.index", false]], "index (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.index", false], [8, "models.OpenAIChatChoice.index", false]], "index (retrieval.retriever attribute)": [[2, "id3", false], [2, "retrieval.Retriever.index", false], [8, "retrieval.Retriever.index", false]], "is_allowed() (security.ratelimiter method)": [[3, "id13", false], [3, "security.RateLimiter.is_allowed", false], [8, "security.RateLimiter.is_allowed", false]], "is_ip_blocked() (security.securitymanager method)": [[3, "id3", false], [3, "id8", false], [3, "security.SecurityManager.is_ip_blocked", false], [8, "security.SecurityManager.is_ip_blocked", false]], "legacychatrequest (class in main)": [[0, "main.LegacyChatRequest", false], [8, "main.LegacyChatRequest", false]], "legacychatresponse (class in main)": [[0, "main.LegacyChatResponse", false], [8, "main.LegacyChatResponse", false]], "logging_config": [[8, "module-logging_config", false]], "lrucache (class in core.cache)": [[8, "core.cache.LRUCache", false]], "main": [[0, "module-main", false], [8, "module-main", false]], "max_tokens (models.chatrequest attribute)": [[1, "id6", false], [1, "models.ChatRequest.max_tokens", false], [8, "id6", false], [8, "models.ChatRequest.max_tokens", false]], "max_tokens (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.max_tokens", false], [8, "models.OpenAIChatRequest.max_tokens", false]], "memory_percent (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.memory_percent", false]], "message (exceptions.symbolicaiexception attribute)": [[8, "exceptions.SymbolicAIException.message", false]], "message (models.chatchoice attribute)": [[1, "id10", false], [1, "models.ChatChoice.message", false], [8, "id10", false], [8, "models.ChatChoice.message", false]], "message (models.documentaddresponse attribute)": [[1, "id25", false], [1, "models.DocumentAddResponse.message", false], [8, "id25", false], [8, "models.DocumentAddResponse.message", false]], "message (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.message", false], [8, "models.OpenAIChatChoice.message", false]], "messages (models.chatrequest attribute)": [[1, "id3", false], [1, "models.ChatRequest.messages", false], [8, "id3", false], [8, "models.ChatRequest.messages", false]], "messages (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.messages", false], [8, "models.OpenAIChatRequest.messages", false]], "metadata (models.documentaddrequest attribute)": [[1, "id22", false], [1, "models.DocumentAddRequest.metadata", false], [8, "id22", false], [8, "models.DocumentAddRequest.metadata", false]], "metadata (models.documentaddresponse attribute)": [[1, "id27", false], [1, "models.DocumentAddResponse.metadata", false], [8, "id27", false], [8, "models.DocumentAddResponse.metadata", false]], "model (models.chatrequest attribute)": [[1, "id4", false], [1, "models.ChatRequest.model", false], [8, "id4", false], [8, "models.ChatRequest.model", false]], "model (models.chatresponse attribute)": [[1, "id18", false], [1, "models.ChatResponse.model", false], [8, "id18", false], [8, "models.ChatResponse.model", false]], "model (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.model", false], [8, "models.OpenAIChatRequest.model", false]], "model (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.model", false], [8, "models.OpenAIChatResponse.model", false]], "model (symbolic_reasoning.symbolicreasoner attribute)": [[4, "id3", false], [4, "symbolic_reasoning.SymbolicReasoner.model", false], [8, "symbolic_reasoning.SymbolicReasoner.model", false]], "model_config (main.legacychatrequest attribute)": [[0, "main.LegacyChatRequest.model_config", false], [8, "main.LegacyChatRequest.model_config", false]], "model_config (main.legacychatresponse attribute)": [[0, "main.LegacyChatResponse.model_config", false], [8, "main.LegacyChatResponse.model_config", false]], "model_config (models.chatchoice attribute)": [[1, "models.ChatChoice.model_config", false], [8, "models.ChatChoice.model_config", false]], "model_config (models.chatmessage attribute)": [[1, "models.ChatMessage.model_config", false], [8, "models.ChatMessage.model_config", false]], "model_config (models.chatrequest attribute)": [[1, "models.ChatRequest.model_config", false], [8, "models.ChatRequest.model_config", false]], "model_config (models.chatresponse attribute)": [[1, "models.ChatResponse.model_config", false], [8, "models.ChatResponse.model_config", false]], "model_config (models.documentaddrequest attribute)": [[1, "models.DocumentAddRequest.model_config", false], [8, "models.DocumentAddRequest.model_config", false]], "model_config (models.documentaddresponse attribute)": [[1, "models.DocumentAddResponse.model_config", false], [8, "models.DocumentAddResponse.model_config", false]], "model_config (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.model_config", false], [8, "models.OpenAIChatChoice.model_config", false]], "model_config (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.model_config", false], [8, "models.OpenAIChatRequest.model_config", false]], "model_config (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.model_config", false], [8, "models.OpenAIChatResponse.model_config", false]], "model_config (models.openaimessage attribute)": [[1, "models.OpenAIMessage.model_config", false], [8, "models.OpenAIMessage.model_config", false]], "model_config (models.performancemetrics attribute)": [[1, "id52", false], [1, "models.PerformanceMetrics.model_config", false], [8, "models.PerformanceMetrics.model_config", false]], "model_config (models.systeminfo attribute)": [[1, "id42", false], [1, "models.SystemInfo.model_config", false], [8, "models.SystemInfo.model_config", false]], "model_config (models.tokenusage attribute)": [[1, "models.TokenUsage.model_config", false], [8, "models.TokenUsage.model_config", false]], "modelrole (class in models)": [[1, "models.ModelRole", false], [8, "models.ModelRole", false]], "models": [[1, "module-models", false], [8, "module-models", false]], "module": [[0, "module-main", false], [1, "module-models", false], [2, "module-retrieval", false], [3, "module-security", false], [4, "module-symbolic_reasoning", false], [8, "module-core.cache", false], [8, "module-exceptions", false], [8, "module-logging_config", false], [8, "module-main", false], [8, "module-models", false], [8, "module-monitoring", false], [8, "module-retrieval", false], [8, "module-security", false], [8, "module-symbolic_reasoning", false], [8, "module-vector_store", false]], "monitoring": [[8, "module-monitoring", false]], "object (models.chatresponse attribute)": [[1, "id16", false], [1, "models.ChatResponse.object", false], [8, "id16", false], [8, "models.ChatResponse.object", false]], "object (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.object", false], [8, "models.OpenAIChatResponse.object", false]], "openai_chat() (in module main)": [[0, "main.openai_chat", false], [8, "main.openai_chat", false]], "openai_chat_stream() (in module main)": [[0, "main.openai_chat_stream", false], [8, "main.openai_chat_stream", false]], "openaichatchoice (class in models)": [[1, "models.OpenAIChatChoice", false], [8, "models.OpenAIChatChoice", false]], "openaichatrequest (class in models)": [[1, "models.OpenAIChatRequest", false], [8, "models.OpenAIChatRequest", false]], "openaichatresponse (class in models)": [[1, "models.OpenAIChatResponse", false], [8, "models.OpenAIChatResponse", false]], "openaimessage (class in models)": [[1, "models.OpenAIMessage", false], [8, "models.OpenAIMessage", false]], "optimize_gpu_memory() (in module main)": [[0, "main.optimize_gpu_memory", false], [8, "main.optimize_gpu_memory", false]], "optimize_index() (retrieval.retriever method)": [[2, "id12", false], [2, "retrieval.Retriever.optimize_index", false], [8, "retrieval.Retriever.optimize_index", false]], "performance_stats() (in module main)": [[0, "main.performance_stats", false], [8, "main.performance_stats", false]], "performancefilter (class in logging_config)": [[8, "logging_config.PerformanceFilter", false]], "performancemetrics (class in models)": [[1, "id48", false], [1, "models.PerformanceMetrics", false], [8, "models.PerformanceMetrics", false]], "performancemonitor (class in monitoring)": [[8, "monitoring.PerformanceMonitor", false]], "process_query() (in module main)": [[0, "main.process_query", false], [8, "main.process_query", false]], "process_query() (symbolic_reasoning.symbolicreasoner method)": [[4, "id5", false], [4, "id9", false], [4, "symbolic_reasoning.SymbolicReasoner.process_query", false], [8, "symbolic_reasoning.SymbolicReasoner.process_query", false]], "prompt_tokens (models.tokenusage attribute)": [[1, "id12", false], [1, "models.TokenUsage.prompt_tokens", false], [8, "id12", false], [8, "models.TokenUsage.prompt_tokens", false]], "rate_limit_middleware() (in module main)": [[0, "main.rate_limit_middleware", false], [8, "main.rate_limit_middleware", false]], "ratelimiter (class in security)": [[3, "id11", false], [3, "security.RateLimiter", false], [8, "security.RateLimiter", false]], "ratelimiterror": [[8, "exceptions.RateLimitError", false]], "read_root() (in module main)": [[0, "main.read_root", false], [8, "main.read_root", false]], "reasoner_info (models.systeminfo attribute)": [[1, "id31", false], [1, "id40", false], [1, "id46", false], [1, "models.SystemInfo.reasoner_info", false], [8, "id31", false], [8, "models.SystemInfo.reasoner_info", false]], "reasoning_time (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.reasoning_time", false]], "reasoningerror": [[8, "exceptions.ReasoningError", false]], "record_cache_hit() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.record_cache_hit", false]], "record_cache_miss() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.record_cache_miss", false]], "record_failed_attempt() (security.securitymanager method)": [[3, "id4", false], [3, "id9", false], [3, "security.SecurityManager.record_failed_attempt", false], [8, "security.SecurityManager.record_failed_attempt", false]], "record_reasoning_time() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.record_reasoning_time", false]], "record_retrieval_time() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.record_retrieval_time", false]], "record_token_count() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.record_token_count", false]], "request_id (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.request_id", false]], "requestmetrics (class in monitoring)": [[8, "monitoring.RequestMetrics", false]], "requests (models.performancemetrics attribute)": [[1, "id35", false], [1, "id51", false], [1, "id55", false], [1, "models.PerformanceMetrics.requests", false], [8, "id35", false], [8, "models.PerformanceMetrics.requests", false]], "resourcenotfounderror": [[8, "exceptions.ResourceNotFoundError", false]], "response (main.legacychatresponse attribute)": [[0, "main.LegacyChatResponse.response", false], [8, "main.LegacyChatResponse.response", false]], "responsecache (class in main)": [[0, "main.ResponseCache", false], [8, "main.ResponseCache", false]], "retrieval": [[2, "module-retrieval", false], [8, "module-retrieval", false]], "retrieval_time (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.retrieval_time", false]], "retrievalerror": [[8, "exceptions.RetrievalError", false]], "retriever (class in retrieval)": [[2, "id0", false], [2, "retrieval.Retriever", false], [8, "retrieval.Retriever", false]], "retriever_info (models.systeminfo attribute)": [[1, "id32", false], [1, "id41", false], [1, "id47", false], [1, "models.SystemInfo.retriever_info", false], [8, "id32", false], [8, "models.SystemInfo.retriever_info", false]], "role (models.chatmessage attribute)": [[1, "id0", false], [1, "models.ChatMessage.role", false], [8, "id0", false], [8, "models.ChatMessage.role", false]], "role (models.openaimessage attribute)": [[1, "models.OpenAIMessage.role", false], [8, "models.OpenAIMessage.role", false]], "sanitize_input() (in module security.securitymanager)": [[3, "id14", false]], "sanitize_input() (security.securitymanager method)": [[3, "id10", false], [3, "id5", false], [3, "security.SecurityManager.sanitize_input", false], [8, "security.SecurityManager.sanitize_input", false]], "search() (retrieval.retriever method)": [[2, "id10", false], [2, "id17", false], [2, "retrieval.Retriever.search", false], [8, "retrieval.Retriever.search", false]], "search() (vector_store.torchvectorstore method)": [[8, "vector_store.TorchVectorStore.search", false]], "security": [[3, "module-security", false], [8, "module-security", false]], "securitymanager (class in security)": [[3, "id0", false], [3, "security.SecurityManager", false], [8, "security.SecurityManager", false]], "serviceunavailableerror": [[8, "exceptions.ServiceUnavailableError", false]], "set() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.set", false]], "set() (main.responsecache method)": [[0, "main.ResponseCache.set", false], [8, "main.ResponseCache.set", false]], "set_context() (logging_config.contextfilter method)": [[8, "logging_config.ContextFilter.set_context", false]], "setup_logging() (in module logging_config)": [[8, "logging_config.setup_logging", false]], "setup_structured_logging() (in module logging_config)": [[8, "logging_config.setup_structured_logging", false]], "setup_vector_db() (retrieval.retriever method)": [[2, "id8", false], [2, "retrieval.Retriever.setup_vector_db", false], [8, "retrieval.Retriever.setup_vector_db", false]], "shutdown() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.shutdown", false]], "shutdown_event() (in module main)": [[0, "main.shutdown_event", false], [8, "main.shutdown_event", false]], "size() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.size", false]], "size() (main.responsecache method)": [[0, "main.ResponseCache.size", false], [8, "main.ResponseCache.size", false]], "start_request() (monitoring.performancemonitor method)": [[8, "monitoring.PerformanceMonitor.start_request", false]], "start_time (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.start_time", false]], "stats() (core.cache.lrucache method)": [[8, "core.cache.LRUCache.stats", false]], "status_code (exceptions.symbolicaiexception attribute)": [[8, "exceptions.SymbolicAIException.status_code", false]], "stream (models.chatrequest attribute)": [[1, "id7", false], [1, "models.ChatRequest.stream", false], [8, "id7", false], [8, "models.ChatRequest.stream", false]], "stream (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.stream", false], [8, "models.OpenAIChatRequest.stream", false]], "structuredformatter (class in logging_config)": [[8, "logging_config.StructuredFormatter", false]], "success (models.documentaddresponse attribute)": [[1, "id24", false], [1, "models.DocumentAddResponse.success", false], [8, "id24", false], [8, "models.DocumentAddResponse.success", false]], "symbolic_ai_exception_handler() (in module main)": [[0, "main.symbolic_ai_exception_handler", false], [8, "main.symbolic_ai_exception_handler", false]], "symbolic_reasoning": [[4, "module-symbolic_reasoning", false], [8, "module-symbolic_reasoning", false]], "symbolicaiexception": [[8, "exceptions.SymbolicAIException", false]], "symbolicreasoner (class in symbolic_reasoning)": [[4, "id0", false], [4, "symbolic_reasoning.SymbolicReasoner", false], [8, "symbolic_reasoning.SymbolicReasoner", false]], "system (models.modelrole attribute)": [[1, "models.ModelRole.SYSTEM", false], [8, "models.ModelRole.SYSTEM", false]], "system (models.performancemetrics attribute)": [[1, "id34", false], [1, "id50", false], [1, "id54", false], [1, "models.PerformanceMetrics.system", false], [8, "id34", false], [8, "models.PerformanceMetrics.system", false]], "system_info() (in module main)": [[0, "main.system_info", false], [8, "main.system_info", false]], "systeminfo (class in models)": [[1, "id36", false], [1, "models.SystemInfo", false], [8, "models.SystemInfo", false]], "systemmetrics (class in monitoring)": [[8, "monitoring.SystemMetrics", false]], "temperature (models.chatrequest attribute)": [[1, "id5", false], [1, "models.ChatRequest.temperature", false], [8, "id5", false], [8, "models.ChatRequest.temperature", false]], "temperature (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.temperature", false], [8, "models.OpenAIChatRequest.temperature", false]], "text (main.legacychatrequest attribute)": [[0, "main.LegacyChatRequest.text", false], [8, "main.LegacyChatRequest.text", false]], "text (models.documentaddrequest attribute)": [[1, "id21", false], [1, "models.DocumentAddRequest.text", false], [8, "id21", false], [8, "models.DocumentAddRequest.text", false]], "timestamp (models.chatmessage attribute)": [[1, "id2", false], [1, "models.ChatMessage.timestamp", false], [8, "id2", false], [8, "models.ChatMessage.timestamp", false]], "timestamp (monitoring.systemmetrics attribute)": [[8, "monitoring.SystemMetrics.timestamp", false]], "to_dict() (exceptions.symbolicaiexception method)": [[8, "exceptions.SymbolicAIException.to_dict", false]], "tokenusage (class in models)": [[1, "models.TokenUsage", false], [8, "models.TokenUsage", false]], "top_p (models.chatrequest attribute)": [[1, "id8", false], [1, "models.ChatRequest.top_p", false], [8, "id8", false], [8, "models.ChatRequest.top_p", false]], "torchvectorstore (class in vector_store)": [[8, "vector_store.TorchVectorStore", false]], "total_tokens (models.tokenusage attribute)": [[1, "id14", false], [1, "models.TokenUsage.total_tokens", false], [8, "id14", false], [8, "models.TokenUsage.total_tokens", false]], "total_tokens (monitoring.requestmetrics attribute)": [[8, "monitoring.RequestMetrics.total_tokens", false]], "usage (models.chatresponse attribute)": [[1, "id20", false], [1, "models.ChatResponse.usage", false], [8, "id20", false], [8, "models.ChatResponse.usage", false]], "usage (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.usage", false], [8, "models.OpenAIChatResponse.usage", false]], "use_gpu (retrieval.retriever attribute)": [[2, "id1", false], [2, "retrieval.Retriever.use_gpu", false], [8, "retrieval.Retriever.use_gpu", false]], "use_gpu (symbolic_reasoning.symbolicreasoner attribute)": [[4, "id1", false], [4, "symbolic_reasoning.SymbolicReasoner.use_gpu", false], [8, "symbolic_reasoning.SymbolicReasoner.use_gpu", false]], "user (models.modelrole attribute)": [[1, "models.ModelRole.USER", false], [8, "models.ModelRole.USER", false]], "using_torch_fallback (retrieval.retriever attribute)": [[2, "id6", false], [2, "retrieval.Retriever.using_torch_fallback", false], [8, "retrieval.Retriever.using_torch_fallback", false]], "validate_api_key() (security.securitymanager method)": [[3, "id2", false], [3, "id7", false], [3, "security.SecurityManager.validate_api_key", false], [8, "security.SecurityManager.validate_api_key", false]], "validate_content() (models.chatmessage class method)": [[1, "models.ChatMessage.validate_content", false], [8, "models.ChatMessage.validate_content", false]], "validate_cors_origin() (in module security)": [[3, "id17", false], [3, "security.validate_cors_origin", false], [8, "security.validate_cors_origin", false]], "validate_messages() (models.chatrequest class method)": [[1, "models.ChatRequest.validate_messages", false], [8, "models.ChatRequest.validate_messages", false]], "validate_text() (models.documentaddrequest class method)": [[1, "models.DocumentAddRequest.validate_text", false], [8, "models.DocumentAddRequest.validate_text", false]], "validate_total_tokens() (models.tokenusage class method)": [[1, "models.TokenUsage.validate_total_tokens", false], [8, "models.TokenUsage.validate_total_tokens", false]], "validation_exception_handler() (in module main)": [[0, "main.validation_exception_handler", false], [8, "main.validation_exception_handler", false]], "validationerror": [[8, "exceptions.ValidationError", false]], "vector_db (retrieval.retriever attribute)": [[2, "id2", false], [2, "retrieval.Retriever.vector_db", false], [8, "retrieval.Retriever.vector_db", false]], "vector_store": [[8, "module-vector_store", false]], "vectorstoreerror": [[8, "exceptions.VectorStoreError", false]], "verify_api_key() (in module security)": [[3, "security.verify_api_key", false], [8, "security.verify_api_key", false]]}, "objects": {"": [[8, 0, 0, "-", "exceptions"], [8, 0, 0, "-", "logging_config"], [8, 0, 0, "-", "main"], [8, 0, 0, "-", "models"], [8, 0, 0, "-", "monitoring"], [8, 0, 0, "-", "retrieval"], [8, 0, 0, "-", "security"], [8, 0, 0, "-", "symbolic_reasoning"], [8, 0, 0, "-", "vector_store"]], "core": [[8, 0, 0, "-", "cache"]], "core.cache": [[8, 1, 1, "", "CacheManager"], [8, 1, 1, "", "LRUCache"], [8, 3, 1, "", "get_cache"]], "core.cache.CacheManager": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "cleanup_all"], [8, 2, 1, "", "create_cache"], [8, 2, 1, "", "get_all_stats"], [8, 2, 1, "", "get_cache"]], "core.cache.LRUCache": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "cleanup_expired"], [8, 2, 1, "", "clear"], [8, 2, 1, "", "delete"], [8, 2, 1, "", "get"], [8, 2, 1, "", "set"], [8, 2, 1, "", "size"], [8, 2, 1, "", "stats"]], "exceptions": [[8, 4, 1, "", "AuthenticationError"], [8, 4, 1, "", "AuthorizationError"], [8, 4, 1, "", "ConfigurationError"], [8, 4, 1, "", "RateLimitError"], [8, 4, 1, "", "ReasoningError"], [8, 4, 1, "", "ResourceNotFoundError"], [8, 4, 1, "", "RetrievalError"], [8, 4, 1, "", "ServiceUnavailableError"], [8, 4, 1, "", "SymbolicAIException"], [8, 4, 1, "", "ValidationError"], [8, 4, 1, "", "VectorStoreError"], [8, 3, 1, "", "handle_exception"]], "exceptions.AuthenticationError": [[8, 2, 1, "", "__init__"]], "exceptions.AuthorizationError": [[8, 2, 1, "", "__init__"]], "exceptions.ConfigurationError": [[8, 2, 1, "", "__init__"]], "exceptions.RateLimitError": [[8, 2, 1, "", "__init__"]], "exceptions.ReasoningError": [[8, 2, 1, "", "__init__"]], "exceptions.ResourceNotFoundError": [[8, 2, 1, "", "__init__"]], "exceptions.RetrievalError": [[8, 2, 1, "", "__init__"]], "exceptions.ServiceUnavailableError": [[8, 2, 1, "", "__init__"]], "exceptions.SymbolicAIException": [[8, 2, 1, "", "__init__"], [8, 5, 1, "", "details"], [8, 5, 1, "", "error_code"], [8, 5, 1, "", "message"], [8, 5, 1, "", "status_code"], [8, 2, 1, "", "to_dict"]], "exceptions.ValidationError": [[8, 2, 1, "", "__init__"]], "exceptions.VectorStoreError": [[8, 2, 1, "", "__init__"]], "logging_config": [[8, 1, 1, "", "ContextFilter"], [8, 1, 1, "", "PerformanceFilter"], [8, 1, 1, "", "StructuredFormatter"], [8, 3, 1, "", "get_logger"], [8, 3, 1, "", "setup_logging"], [8, 3, 1, "", "setup_structured_logging"]], "logging_config.ContextFilter": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "clear_context"], [8, 2, 1, "", "filter"], [8, 2, 1, "", "set_context"]], "logging_config.PerformanceFilter": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "filter"]], "logging_config.StructuredFormatter": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "format"]], "main": [[8, 1, 1, "", "LegacyChatRequest"], [8, 1, 1, "", "LegacyChatResponse"], [8, 1, 1, "", "ResponseCache"], [8, 3, 1, "", "add_document"], [8, 3, 1, "", "add_security_headers"], [8, 3, 1, "", "api_info"], [8, 3, 1, "", "authentication_exception_handler"], [8, 3, 1, "", "chat"], [8, 3, 1, "", "chat_stream"], [8, 3, 1, "", "check_request_size_middleware"], [8, 3, 1, "", "clean_cache"], [8, 3, 1, "", "general_exception_handler"], [8, 3, 1, "", "get_document_count"], [8, 3, 1, "", "openai_chat"], [8, 3, 1, "", "openai_chat_stream"], [8, 3, 1, "", "optimize_gpu_memory"], [8, 3, 1, "", "performance_stats"], [8, 3, 1, "", "process_query"], [8, 3, 1, "", "rate_limit_middleware"], [8, 3, 1, "", "read_root"], [8, 3, 1, "", "shutdown_event"], [8, 3, 1, "", "symbolic_ai_exception_handler"], [8, 3, 1, "", "system_info"], [8, 3, 1, "", "validation_exception_handler"]], "main.LegacyChatRequest": [[8, 5, 1, "", "model_config"], [8, 5, 1, "", "text"]], "main.LegacyChatResponse": [[8, 5, 1, "", "cached"], [8, 5, 1, "", "model_config"], [8, 5, 1, "", "response"]], "main.ResponseCache": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "clean"], [8, 2, 1, "", "get"], [8, 2, 1, "", "set"], [8, 2, 1, "", "size"]], "models": [[8, 1, 1, "", "ChatChoice"], [8, 1, 1, "", "ChatMessage"], [8, 1, 1, "", "ChatRequest"], [8, 1, 1, "", "ChatResponse"], [8, 1, 1, "", "DocumentAddRequest"], [8, 1, 1, "", "DocumentAddResponse"], [8, 1, 1, "", "ModelRole"], [8, 1, 1, "", "OpenAIChatChoice"], [8, 1, 1, "", "OpenAIChatRequest"], [8, 1, 1, "", "OpenAIChatResponse"], [8, 1, 1, "", "OpenAIMessage"], [8, 1, 1, "", "PerformanceMetrics"], [8, 1, 1, "", "SystemInfo"], [8, 1, 1, "", "TokenUsage"]], "models.ChatChoice": [[8, 5, 1, "id11", "finish_reason"], [8, 5, 1, "id9", "index"], [8, 5, 1, "id10", "message"], [8, 5, 1, "", "model_config"]], "models.ChatMessage": [[8, 5, 1, "id1", "content"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id0", "role"], [8, 5, 1, "id2", "timestamp"], [8, 2, 1, "", "validate_content"]], "models.ChatRequest": [[8, 5, 1, "id6", "max_tokens"], [8, 5, 1, "id3", "messages"], [8, 5, 1, "id4", "model"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id7", "stream"], [8, 5, 1, "id5", "temperature"], [8, 5, 1, "id8", "top_p"], [8, 2, 1, "", "validate_messages"]], "models.ChatResponse": [[8, 5, 1, "id19", "choices"], [8, 5, 1, "id17", "created"], [8, 5, 1, "id15", "id"], [8, 5, 1, "id18", "model"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id16", "object"], [8, 5, 1, "id20", "usage"]], "models.DocumentAddRequest": [[8, 5, 1, "id23", "document_id"], [8, 5, 1, "id22", "metadata"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id21", "text"], [8, 2, 1, "", "validate_text"]], "models.DocumentAddResponse": [[8, 5, 1, "id26", "document_id"], [8, 5, 1, "id25", "message"], [8, 5, 1, "id27", "metadata"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id24", "success"]], "models.ModelRole": [[8, 5, 1, "", "ASSISTANT"], [8, 5, 1, "", "SYSTEM"], [8, 5, 1, "", "USER"], [8, 2, 1, "", "__format__"]], "models.OpenAIChatChoice": [[8, 5, 1, "", "finish_reason"], [8, 5, 1, "", "index"], [8, 5, 1, "", "message"], [8, 5, 1, "", "model_config"]], "models.OpenAIChatRequest": [[8, 5, 1, "", "max_tokens"], [8, 5, 1, "", "messages"], [8, 5, 1, "", "model"], [8, 5, 1, "", "model_config"], [8, 5, 1, "", "stream"], [8, 5, 1, "", "temperature"]], "models.OpenAIChatResponse": [[8, 5, 1, "", "choices"], [8, 5, 1, "", "created"], [8, 5, 1, "", "id"], [8, 5, 1, "", "model"], [8, 5, 1, "", "model_config"], [8, 5, 1, "", "object"], [8, 5, 1, "", "usage"]], "models.OpenAIMessage": [[8, 5, 1, "", "content"], [8, 5, 1, "", "model_config"], [8, 5, 1, "", "role"]], "models.PerformanceMetrics": [[8, 5, 1, "id33", "cache"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id35", "requests"], [8, 5, 1, "id34", "system"]], "models.SystemInfo": [[8, 5, 1, "id28", "gpu_available"], [8, 5, 1, "id29", "gpu_name"], [8, 5, 1, "id30", "gpu_optimized"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id31", "reasoner_info"], [8, 5, 1, "id32", "retriever_info"]], "models.TokenUsage": [[8, 5, 1, "id13", "completion_tokens"], [8, 5, 1, "", "model_config"], [8, 5, 1, "id12", "prompt_tokens"], [8, 5, 1, "id14", "total_tokens"], [8, 2, 1, "", "validate_total_tokens"]], "monitoring": [[8, 1, 1, "", "PerformanceMonitor"], [8, 1, 1, "", "RequestMetrics"], [8, 1, 1, "", "SystemMetrics"]], "monitoring.PerformanceMonitor": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "end_request"], [8, 2, 1, "", "get_recent_metrics"], [8, 2, 1, "", "record_cache_hit"], [8, 2, 1, "", "record_cache_miss"], [8, 2, 1, "", "record_reasoning_time"], [8, 2, 1, "", "record_retrieval_time"], [8, 2, 1, "", "record_token_count"], [8, 2, 1, "", "shutdown"], [8, 2, 1, "", "start_request"]], "monitoring.RequestMetrics": [[8, 2, 1, "", "__init__"], [8, 5, 1, "", "cached"], [8, 2, 1, "", "duration"], [8, 5, 1, "", "end_time"], [8, 5, 1, "", "endpoint"], [8, 5, 1, "", "error"], [8, 5, 1, "", "reasoning_time"], [8, 5, 1, "", "request_id"], [8, 5, 1, "", "retrieval_time"], [8, 5, 1, "", "start_time"], [8, 5, 1, "", "total_tokens"]], "monitoring.SystemMetrics": [[8, 2, 1, "", "__init__"], [8, 5, 1, "", "active_requests"], [8, 5, 1, "", "cache_hits"], [8, 5, 1, "", "cache_misses"], [8, 5, 1, "", "cache_size"], [8, 5, 1, "", "cpu_percent"], [8, 5, 1, "", "gpu_memory_used"], [8, 5, 1, "", "gpu_utilization"], [8, 5, 1, "", "memory_percent"], [8, 5, 1, "", "timestamp"]], "retrieval": [[8, 1, 1, "", "Retriever"]], "retrieval.Retriever": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "add_documents"], [8, 2, 1, "", "batch_add_documents"], [8, 5, 1, "", "dimension"], [8, 5, 1, "", "documents"], [8, 2, 1, "", "get_system_info"], [8, 5, 1, "", "index"], [8, 2, 1, "", "optimize_index"], [8, 2, 1, "", "search"], [8, 2, 1, "", "setup_vector_db"], [8, 5, 1, "", "use_gpu"], [8, 5, 1, "", "using_torch_fallback"], [8, 5, 1, "", "vector_db"]], "security": [[8, 1, 1, "", "RateLimiter"], [8, 1, 1, "", "SecurityManager"], [8, 3, 1, "", "check_request_size"], [8, 3, 1, "", "get_client_ip"], [8, 3, 1, "", "get_cors_config"], [8, 3, 1, "", "get_security_headers"], [8, 3, 1, "", "validate_cors_origin"], [8, 3, 1, "", "verify_api_key"]], "security.RateLimiter": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "is_allowed"]], "security.SecurityManager": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "is_ip_blocked"], [8, 2, 1, "", "record_failed_attempt"], [8, 2, 1, "", "sanitize_input"], [8, 2, 1, "", "validate_api_key"]], "symbolic_reasoning": [[8, 1, 1, "", "SymbolicReasoner"]], "symbolic_reasoning.SymbolicReasoner": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "batch_process_queries"], [8, 5, 1, "", "engine"], [8, 2, 1, "", "get_system_info"], [8, 5, 1, "", "model"], [8, 2, 1, "", "process_query"], [8, 5, 1, "", "use_gpu"]], "vector_store": [[8, 1, 1, "", "TorchVectorStore"]], "vector_store.TorchVectorStore": [[8, 2, 1, "", "__init__"], [8, 2, 1, "", "add"], [8, 2, 1, "", "get_system_info"], [8, 2, 1, "", "search"]]}, "objnames": {"0": ["py", "module", "Python module"], "1": ["py", "class", "Python class"], "2": ["py", "method", "Python method"], "3": ["py", "function", "Python function"], "4": ["py", "exception", "Python exception"], "5": ["py", "attribute", "Python attribute"]}, "objtypes": {"0": "py:module", "1": "py:class", "2": "py:method", "3": "py:function", "4": "py:exception", "5": "py:attribute"}, "terms": {"": [0, 2, 4, 6, 7, 8], "0": [0, 1, 2, 4, 5, 6, 8], "00": [1, 8], "000": [2, 4, 8], "00z": [1, 8], "06": [1, 3, 4, 8], "1": [0, 1, 2, 3, 4, 5, 6, 8], "10": [1, 2, 4, 5, 8], "100": [1, 2, 3, 5, 6, 8], "1000": [0, 1, 6, 8], "10000": [1, 2, 3, 6, 8], "100000": [1, 8], "10485760": [6, 8], "10mb": [5, 6], "12": [1, 8], "123": [1, 5, 8], "12345": 6, "127": 6, "15": 3, "150": [0, 1, 5], "1677652288": [1, 8], "1677652348": 5, "168": 3, "192": 3, "2": [1, 2, 8], "20": [1, 8], "2025": [1, 3, 4, 8], "21": [1, 8], "234": [2, 8], "25": 1, "29": [1, 3, 4, 8], "29t12": [1, 8], "3": [2, 4, 6, 8], "30": 1, "300": 6, "3000": [3, 6, 8], "32": [2, 8], "3600": [6, 8], "3f": [2, 8], "4": [4, 6], "400": [0, 5], "401": [0, 5], "403": [3, 5], "4090": [4, 8], "4096": [1, 8], "429": [0, 3, 5], "5": [1, 2, 3, 4, 6, 8], "50": [2, 8], "500": [0, 4, 5, 8], "60": [1, 3, 6, 8], "6379": 6, "67890": 6, "7": [0, 1, 2, 5, 8], "768": [2, 6, 8], "8": [1, 6], "80": 1, "8000": [0, 5, 6, 7], "85": 7, "856": [2, 8], "9": [1, 8], "95": 5, "A": [4, 8], "At": 1, "For": [3, 4, 8], "If": [1, 2, 3, 4, 8], "In": [2, 4, 8], "It": [2, 4, 8], "The": [0, 1, 2, 3, 4, 5, 6, 7, 8], "These": 5, "To": 1, "_": 3, "__format__": [1, 8], "__init__": [0, 2, 3, 4, 8], "__str__": [1, 8], "about": [1, 2, 4, 8], "acceler": [4, 6, 7, 8], "access": [3, 4, 5, 8], "accord": [4, 8], "accordingli": [4, 8], "accur": 3, "accuraci": [2, 4, 8], "active_request": [1, 8], "actual": [1, 8], "ad": [1, 2, 4, 8], "add": [0, 1, 2, 3, 6, 8], "add_docu": [0, 2, 7, 8], "add_middlewar": 3, "add_security_head": [0, 7, 8], "addit": [1, 4, 8], "additional_context": 5, "address": [0, 3, 8], "adjust": [3, 6], "advanc": [7, 8], "after": [2, 3, 8], "ai": [0, 1, 2, 3, 4, 7, 8], "alert": 7, "algorithm": 2, "all": [0, 1, 2, 3, 4, 5, 6, 8], "allow": [3, 5, 8], "allow_origin": [3, 8], "allowed_origin": [3, 8], "alpin": 6, "alwai": [1, 6, 8], "an": [3, 4, 7, 8], "ani": [1, 3, 4, 8], "annot": [1, 8], "annotated_typ": [1, 8], "anthrop": [6, 8], "anthropic_api_kei": 4, "api": [0, 1, 4, 6], "api_info": [0, 7, 8], "api_kei": [3, 6, 8], "app": [3, 6, 8], "app_": 6, "app_debug": [0, 6], "app_environ": 6, "app_host": [0, 6], "app_port": [0, 6], "app_reload": 6, "app_titl": 6, "app_vers": 6, "app_work": [0, 6], "append": 6, "appli": [0, 2, 4, 8], "applic": [1, 2, 3, 5, 7, 8], "appropri": [1, 2, 3, 6], "approxim": 2, "appset": 6, "ar": [0, 1, 2, 3, 4, 8], "argument": 8, "around": [2, 8], "arrai": [0, 5, 6, 8], "asctim": 6, "assist": [1, 3, 4, 5, 8], "assistant_msg": 1, "associ": 8, "async": [0, 3, 7, 8], "attack": 3, "attempt": [7, 8], "augment": [2, 8], "authent": [0, 6, 7, 8], "authentication_error": [0, 5], "authentication_exception_handl": [0, 7, 8], "authenticationerror": [0, 3, 8], "author": [0, 1, 3, 4, 5, 6, 7, 8], "authorization_error": 5, "authorizationerror": 8, "automat": [0, 1, 2, 4, 7, 8], "avail": [0, 1, 2, 4, 5, 6, 8], "avoid": [2, 3], "await": [3, 7], "b": [4, 8], "baai": [2, 6], "back": 8, "backend": [4, 7, 8], "background": [4, 8], "background_task": [0, 8], "backgroundtask": [0, 8], "backup": 8, "backup_count": 8, "balanc": [2, 3, 7], "base": [0, 1, 2, 3, 4, 6, 7, 8], "basemodel": [0, 1], "baseset": 6, "basic": [6, 8], "batch": 8, "batch_add_docu": [2, 8], "batch_process_queri": [4, 8], "batch_siz": [2, 8], "bearer": [0, 3, 5, 6, 7], "been": [1, 8], "befor": [2, 3, 6, 8], "being": [2, 4, 8], "benefit": 4, "bert": [2, 8], "best": [7, 8], "better": [2, 4, 8], "between": [1, 2, 8], "bge": [2, 6], "block": [5, 6, 7, 8], "block_dur": 6, "bool": [0, 1, 2, 3, 4, 8], "boolean": 5, "both": [0, 2, 8], "boundari": 1, "build": [6, 7], "built": [2, 4], "burst": 3, "busi": [1, 6], "byte": [3, 8], "c": [2, 4, 6, 8], "cach": [0, 1, 3, 7, 8], "cache_cleanup_interv": 6, "cache_hit": 8, "cache_max_s": 6, "cache_miss": 8, "cache_redis_url": 6, "cache_s": 8, "cache_ttl_second": 6, "cachemanag": 8, "cachemetr": 1, "call": [2, 5, 8], "call_next": [0, 3, 8], "can": [1, 2, 3, 4, 5, 6, 8], "cannot": [0, 1, 4, 8], "capabl": [2, 4, 7, 8], "case": [2, 6, 7], "categori": 2, "caus": 8, "certain": 1, "chang": 6, "charact": [2, 3, 4, 8], "chat": [1, 3, 7], "chat_endpoint": 3, "chat_stream": [0, 7, 8], "chatchoic": [1, 7, 8], "chatcmpl": [1, 5, 8], "chatmessag": [1, 7, 8], "chatrequest": [0, 1, 7, 8], "chatrespons": [0, 1, 7, 8], "check": [2, 3, 4, 6, 7, 8], "check_request_s": [3, 7, 8], "check_request_size_middlewar": [0, 7, 8], "choic": [0, 8], "choos": 2, "chosen": [4, 8], "chromadb": [6, 7, 8], "chunk": [1, 5], "cl": 1, "class": [0, 1, 7, 8], "classmethod": [1, 8], "classvar": [0, 1, 8], "claud": [4, 6], "clean": [0, 2, 8], "clean_cach": [0, 7, 8], "clean_text": 3, "cleanup": [0, 3, 8], "cleanup_al": 8, "cleanup_expir": 8, "clear": [1, 7, 8], "clear_context": 8, "cli": 6, "clickjack": 3, "client": [5, 7, 8], "client_ip": 3, "cloud": [4, 8], "code": [0, 1, 4, 8], "collect": [2, 7, 8], "collection_interv": 8, "com": [3, 6, 8], "combin": [2, 4, 7, 8], "commit": 6, "common": [0, 7], "commun": [4, 7, 8], "compar": [4, 8], "comparison": [2, 3, 8], "compat": [0, 1, 4, 7, 8], "complement": [4, 8], "complet": [7, 8], "completion_token": [1, 8], "complex": [1, 4], "compon": 0, "compos": 6, "comprehens": [1, 2, 3, 4, 5, 6, 7, 8], "comput": [2, 7, 8], "concern": 7, "conclud": [4, 8], "conclus": [4, 8], "concurr": [5, 7], "config": [0, 1, 6, 7, 8], "config_data": 6, "config_kei": 8, "configdict": [0, 1, 8], "configur": [1, 5, 7], "configurationerror": [2, 4, 8], "conform": [0, 1, 8], "connect": [3, 4, 6, 7, 8], "consid": [1, 2, 3], "consider": [1, 7, 8], "consist": 2, "consol": 8, "constant": 3, "constraint": [1, 8], "contain": [0, 1, 2, 3, 4, 8], "container": 7, "content": [0, 1, 2, 3, 4, 5, 8], "content_filt": 1, "context": [1, 2, 4, 7, 8], "contextfilt": 8, "contextu": 8, "control": [3, 6, 8], "convers": [4, 5, 8], "convert": [1, 8], "copi": 6, "cor": [0, 6, 7, 8], "core": 0, "correl": 7, "cors_config": [3, 8], "cors_origin": 6, "corsmiddlewar": 3, "cost": 4, "count": [0, 6, 8], "cover": 6, "coverag": 7, "cp": [6, 7], "cpu": [2, 4, 6], "cpu_perc": [1, 8], "creat": [1, 6, 8], "create_cach": 8, "create_env_templ": 6, "creation": [1, 8], "credenti": [3, 5, 8], "critic": 8, "cross": [3, 5, 7, 8], "cuda": [4, 6, 8], "curl": 6, "current": [0, 4, 6, 8], "custom": [0, 2, 8], "data": [2, 5, 6, 7, 8], "databas": [2, 6, 7, 8], "dataset": 2, "date": [1, 3, 4, 8], "datetim": [1, 8], "db": 2, "debug": [0, 6, 8], "decod": 0, "def": [1, 3, 6], "default": [0, 1, 2, 3, 4, 6, 8], "default_max_s": 8, "default_ttl": 8, "defin": [1, 8], "delet": [2, 5, 8], "delta": 1, "deni": [5, 8], "dens": [2, 8], "depend": [2, 3, 4, 7, 8], "depends_on": 6, "deploi": 6, "deploy": 6, "descend": [2, 8], "descript": [1, 5], "deseri": 1, "detail": [0, 1, 2, 3, 4, 5, 7, 8], "detect": [4, 8], "dev": [6, 7], "dict": [0, 1, 2, 3, 4, 8], "dictionari": [0, 2, 3, 4, 8], "differ": [2, 3, 4, 6, 8], "dimens": [2, 8], "direct": 3, "directli": 5, "directori": 8, "disabl": 6, "discuss": [4, 7, 8], "distribut": [2, 8], "doc": 5, "doc1": [2, 8], "doc2": [2, 8], "doc_": 2, "doc_123": [1, 8], "doc_id": 8, "docker": 7, "dockerfil": 6, "docstr": 1, "document": [0, 1, 6, 8], "document_id": [1, 8], "documentaddrequest": [0, 1, 7, 8], "documentaddrespons": [0, 1, 7, 8], "doe": [4, 8], "doesn": [1, 8], "domain": [2, 8], "done": [4, 5], "dr": [1, 8], "drop": 7, "due": [2, 3, 4, 8], "durat": [3, 8], "e": [1, 2, 3, 4, 8], "each": [2, 4, 5, 6, 8], "echo": 6, "edit": [6, 7], "effici": [2, 7, 8], "elk": 7, "els": [3, 6], "embed": [7, 8], "embedding_model": 2, "empti": [0, 1, 2, 3, 4, 8], "en": [2, 6], "enabl": [1, 2, 3, 4, 5, 6, 8], "encod": [4, 8], "end": 8, "end_request": 8, "end_tim": 8, "endpoint": [1, 3, 7], "enforc": 3, "engin": [6, 7, 8], "enhanc": [2, 3, 4, 7, 8], "enterpris": 7, "entri": [0, 8], "enum": 1, "enumer": [1, 8], "env": [6, 7], "environ": [0, 3, 4, 7], "equal": [1, 8], "error": [7, 8], "error_cod": [4, 5, 8], "etc": [4, 8], "event": [0, 8], "evict": 2, "exact": [2, 3], "exampl": [6, 7, 8], "exc": [0, 8], "exceed": [0, 2, 3, 4, 5, 8], "except": [0, 1, 2, 3, 4], "exclud": 1, "expans": 2, "expect": 3, "experi": 2, "expir": 8, "explain": [0, 4, 8], "explan": [4, 8], "export": 6, "expos": 3, "extend": [2, 8], "extra": [1, 8], "f": [2, 4, 6, 8], "facebook": 2, "fact": [4, 8], "factori": [1, 8], "fail": [2, 4, 5, 6, 7, 8], "failur": [2, 3, 4, 8], "faiss": [6, 7, 8], "fallback": [2, 4, 6, 8], "fals": [0, 1, 3, 4, 5, 6, 8], "fast": [2, 7, 8], "fastapi": [3, 7, 8], "faster": [2, 4, 8], "featur": [0, 2, 3, 4, 8], "field": [0, 5, 8], "field_valid": 1, "file": [7, 8], "file_path": 6, "filter": [2, 3, 8], "filtered_result": 2, "finish": 1, "finish_reason": [1, 8], "first": [2, 6, 8], "float": [1, 2, 8], "follow": [0, 2, 4, 7], "forbid": [1, 8], "form": 2, "formal": 2, "format": [0, 1, 2, 6, 8], "format_spec": [1, 8], "formatt": 8, "forward": 3, "found": [0, 2, 8], "foundat": [2, 4, 8], "frame": 3, "framework": [4, 7, 8], "from": [0, 1, 2, 3, 4, 6, 8], "full": [2, 4, 7, 8], "function": [3, 8], "g": [2, 3, 8], "ge": [1, 8], "geforc": [4, 8], "gener": [0, 1, 2, 3, 4, 8], "general_exception_handl": [0, 7, 8], "get": [0, 2, 3, 4, 5, 6, 8], "get_all_stat": 8, "get_cach": 8, "get_client_ip": [3, 7, 8], "get_cors_config": [3, 7, 8], "get_document_count": [0, 7, 8], "get_logg": 8, "get_recent_metr": 8, "get_security_head": [3, 7, 8], "get_set": 6, "get_system_info": [2, 4, 8], "github": 7, "good": 2, "gpt": [4, 6, 8], "gpu": [0, 1, 6, 7, 8], "gpu_avail": [1, 4, 8], "gpu_en": [2, 4, 8], "gpu_memory_us": 8, "gpu_nam": [1, 4, 8], "gpu_optim": [1, 8], "gpu_util": 8, "gracefulli": 1, "grade": 7, "grafana": 7, "grant": 3, "graph": 2, "grep": 6, "gt": [1, 8], "h": 6, "ha": [1, 5, 8], "handl": [1, 7], "handle_except": 8, "harden": 7, "hardwar": [4, 8], "have": [4, 8], "header": [0, 7, 8], "health": 7, "hello": [1, 3, 8], "help": [1, 8], "here": 5, "hierarch": 6, "high": [1, 2, 4, 7], "higher": [2, 6, 8], "highli": 2, "hint": 1, "histori": [4, 8], "hit": [1, 8], "hit_rat": 1, "horizont": 7, "host": [0, 6], "hour": [3, 6], "how": [1, 4, 6, 8], "http": [0, 3, 5, 6, 7, 8], "httpauthorizationcredenti": [3, 8], "httpbearer": [3, 8], "httpexcept": [3, 8], "human": [1, 5, 8], "hybrid": [2, 4, 8], "i": [0, 1, 2, 3, 4, 5, 6, 7, 8], "id": [1, 2, 5, 7, 8], "identifi": [1, 2, 3, 5, 8], "imag": 6, "implement": [0, 2, 3, 4, 8], "impli": [4, 8], "import": [0, 1, 2, 3, 4, 6, 7], "improv": [2, 4, 8], "includ": [1, 2, 3, 4, 5, 6, 8], "include_extra": 8, "include_metadata": 2, "incom": 1, "incorpor": [4, 8], "increment": 1, "index": [1, 7, 8], "index_s": [1, 2], "index_typ": 2, "indexflatip": 2, "indexhnsw": 2, "indexivfflat": 2, "indic": [0, 2, 8], "individu": 1, "info": [1, 2, 4, 6, 8], "inform": [1, 3, 4, 7, 8], "initi": 8, "input": [0, 1, 2, 4, 7, 8], "instal": [4, 7, 8], "instanc": 8, "int": [0, 1, 2, 3, 8], "integ": [2, 5, 8], "integr": [2, 4, 7, 8], "intellig": 7, "interact": 5, "interfac": 8, "internal_error": [0, 5], "internal_field": 1, "internet": [4, 8], "interv": [3, 8], "invalid": [0, 1, 2, 3, 4, 5, 8], "involv": [4, 8], "ip": [5, 7, 8], "ip_address": [3, 8], "is_allow": [3, 8], "is_avail": 6, "is_ip_block": [3, 8], "issu": [4, 7, 8], "item": [3, 8], "iter_lin": 0, "json": [0, 5, 6, 7, 8], "json_data": 1, "json_schema_extra": [1, 8], "jsonrespons": 3, "k": [2, 8], "keep": [0, 8], "kei": [0, 1, 2, 4, 5, 6, 7, 8], "key1": 6, "key2": 6, "knowledg": [4, 8], "kubernet": 7, "kwarg": 8, "l6": 2, "languag": [0, 1, 2, 3, 4, 5, 6, 8], "larg": [2, 3, 4, 8], "large_document_set": 2, "larger": 6, "latenc": 4, "layer": 7, "le": [1, 8], "leak": 2, "learn": [2, 8], "least": 1, "legaci": 8, "legacychatrequest": [0, 7, 8], "legacychatrespons": [0, 7, 8], "legitim": 3, "len": [1, 2, 8], "length": [1, 2, 3, 4, 8], "less": [2, 8], "level": [6, 8], "levelnam": 6, "lightrag": [2, 8], "lightweight": 2, "like": [2, 8], "limit": [0, 4, 6, 7, 8], "line": 0, "list": [1, 2, 3, 4, 5, 8], "live": 8, "llama": [1, 4, 6, 8], "load": [3, 7], "load_config_from_fil": 6, "local": [0, 1, 5, 6, 7, 8], "localhost": [0, 3, 5, 6, 7, 8], "log": [3, 4, 7], "log_backup_count": 6, "log_dir": 8, "log_fil": 8, "log_file_en": 6, "log_file_path": 6, "log_format": 6, "log_level": [6, 8], "log_max_file_s": 6, "log_structured_log": 6, "logger": 8, "logging_config": 8, "logic": [1, 2, 4, 6, 7, 8], "login": 3, "logrecord": 8, "long": [2, 4], "look": 8, "low": 4, "lru": [2, 7, 8], "lrucach": 8, "m": 6, "machin": [1, 2, 8], "mai": [2, 4, 8], "main": [1, 2, 3, 4, 6, 7, 8], "maintain": [3, 8], "mainten": 2, "malici": [3, 8], "manag": [0, 1, 2, 4, 7], "manipul": [4, 8], "map": [3, 8], "match": [1, 8], "max_file_s": 8, "max_length": [1, 3, 8], "max_siz": [0, 1, 6, 8], "max_token": [0, 1, 4, 5, 8], "maximum": [1, 2, 3, 4, 5, 8], "maxlen": [1, 8], "mechan": 2, "medium": 2, "memori": [0, 2, 3, 4, 6, 7, 8], "memory_perc": [1, 8], "messag": [0, 2, 3, 4, 6, 7, 8], "metadata": [1, 2, 8], "method": [5, 8], "methodologi": [4, 8], "metric": [1, 2, 7, 8], "middlewar": 3, "million": 2, "mime": 3, "min_length": [1, 8], "minilm": 2, "minlen": [1, 8], "minut": [3, 5, 8], "miss": [0, 1, 4, 5, 8], "mit": 7, "ml": 2, "mode": [0, 6], "model": [0, 3, 4, 5, 8], "model_config": [0, 1, 8], "model_dump": 1, "model_dump_json": 1, "model_embedding_dimens": 6, "model_embedding_model": 6, "model_gpu_memory_fract": 6, "model_reasoning_engin": 6, "model_reasoning_model": 6, "model_use_gpu": 6, "model_valid": 1, "model_validate_json": 1, "model_vector_db_backend": 6, "modelrol": [1, 7, 8], "modern": [2, 7], "modul": 7, "modular": 7, "monitor": [1, 2, 3, 4], "more": [2, 4, 8], "most": [2, 8], "msg": 1, "multi": 7, "multipl": [2, 4, 6, 7, 8], "must": [1, 2, 4, 8], "n": [2, 4, 8], "name": [1, 2, 3, 4, 5, 6, 8], "natur": [4, 8], "ndarrai": [2, 8], "network": [1, 2, 4, 8], "neural": [0, 1, 2, 3, 4, 5, 6, 8], "never": [3, 6], "new": 8, "non": [2, 3, 4, 8], "none": [0, 1, 2, 3, 4, 8], "nucleu": [1, 5, 8], "null": [3, 6], "number": [0, 2, 3, 5, 8], "numpi": 8, "nvidia": [4, 8], "object": [0, 1, 2, 3, 4, 5, 8], "old": [0, 8], "oldest": [0, 8], "one": 1, "onli": [4, 8], "open": 6, "openai": [0, 1, 6, 7, 8], "openai_api_kei": 4, "openai_chat": [0, 7, 8], "openai_chat_stream": [0, 7, 8], "openaichatchoic": [1, 7, 8], "openaichatrequest": [0, 1, 7, 8], "openaichatrespons": [0, 1, 7, 8], "openaimessag": [1, 7, 8], "openapi": 7, "oper": [0, 1, 2, 3, 4, 5, 7, 8], "optim": [0, 1, 4, 7, 8], "optimize_gpu_memori": [0, 7, 8], "optimize_index": [2, 8], "option": [0, 1, 2, 3, 4, 5, 6, 8], "opu": 4, "order": [2, 8], "organ": 2, "origin": [5, 6, 7, 8], "other": [0, 1, 3, 8], "otherwis": [3, 8], "out": 2, "overrid": 6, "overridden": [1, 8], "overview": 8, "page": 7, "paper": 2, "paramet": [0, 1, 2, 3, 4, 7, 8], "pars": [1, 4, 8], "pass": 3, "path": 8, "pattern": [2, 3, 7, 8], "per": [3, 4, 5, 7], "perfect": [2, 8], "perform": 8, "performance_stat": [0, 7, 8], "performancefilt": 8, "performancemetr": [7, 8], "performancemonitor": 8, "period": 8, "permiss": [4, 8], "persist": 2, "pinecon": 6, "ping": 6, "pip": 7, "polici": [2, 3, 7], "pool": 7, "popul": 6, "port": [0, 6], "posit": [2, 8], "possibl": 3, "post": [0, 3, 5, 7], "power": [2, 8], "practic": [7, 8], "pre": [2, 8], "predict": [4, 8], "preflight": 3, "preprocess": 2, "present": [4, 8], "prevent": [2, 3], "previou": [4, 8], "price": 4, "principl": 4, "print": [0, 1, 2, 3, 4, 6, 7, 8], "privaci": 4, "probe": 7, "process": [0, 1, 3, 7, 8], "process_chat_request": 1, "process_queri": [0, 4, 7, 8], "prod": 6, "product": [2, 3, 4, 8], "profil": 1, "progress": [2, 4], "project": 7, "prometheu": 7, "prompt": [1, 8], "prompt_token": [1, 8], "proper": [1, 2, 4, 8], "properli": [4, 8], "properti": [4, 8], "protect": [0, 3, 7], "protected_endpoint": 3, "provid": [0, 1, 2, 3, 4, 5, 7, 8], "proxi": 3, "public_data": 1, "put": 5, "py": [6, 7], "pydant": [0, 1, 6, 8], "python": [1, 6, 7], "pytorch": [0, 2, 4, 8], "q": 4, "qualiti": [2, 4, 8], "queri": [0, 4, 8], "query_vector": 8, "question": [2, 4, 8], "r": [2, 7], "rais": [1, 2, 3, 4, 8], "random": [1, 2, 4, 5, 8], "randomli": 3, "rang": [2, 8], "rate": [0, 4, 6, 7, 8], "rate_limit": 3, "rate_limit_error": [0, 5], "rate_limit_middlewar": [0, 7, 8], "rate_limit_request": 6, "ratelimit": [5, 7, 8], "ratelimiterror": [3, 8], "read_root": [0, 7, 8], "readabl": [1, 5, 8], "readi": 7, "real": [3, 7], "reason": [0, 1, 2, 5, 6, 7, 8], "reasoner_info": [1, 8], "reasoning_engin": 6, "reasoning_error": 5, "reasoning_tim": 8, "reasoning_typ": 8, "reasoningerror": [4, 8], "recal": 2, "recent": 8, "recommend": 6, "recomput": 2, "record": [3, 8], "record_cache_hit": 8, "record_cache_miss": 8, "record_failed_attempt": [3, 8], "record_reasoning_tim": 8, "record_retrieval_tim": 8, "record_token_count": 8, "redi": [6, 8], "redis_url": 6, "redoc": 5, "referr": 3, "regular": 2, "regularli": [3, 6], "reject": 3, "relev": [2, 4, 8], "reload": 6, "remain": 5, "remot": [4, 8], "remov": [0, 3, 8], "repeat": 3, "replac": [2, 7, 8], "repres": [1, 8], "represent": 8, "request": [0, 6, 7, 8], "request_copi": 1, "request_data": 1, "request_id": 8, "requestmetr": 8, "requir": [1, 4, 5, 7, 8], "rerank": [2, 8], "research_pap": [1, 8], "reset": 5, "resourc": [3, 4, 5, 7, 8], "resource_id": 8, "resource_typ": 8, "resourcenotfounderror": 8, "respons": [0, 3, 4, 7, 8], "response_cont": 1, "responsecach": [0, 7, 8], "rest": 7, "result": [0, 2, 8], "retent": 6, "retri": 8, "retriev": [0, 1, 4, 5, 7, 8], "retrieval_error": 5, "retrieval_tim": 8, "retrievalerror": [2, 8], "retriever_info": [1, 8], "retry_aft": [3, 8], "return": [0, 1, 2, 3, 4, 5, 6, 8], "revers": 3, "review": 3, "rich": 2, "role": [0, 1, 5, 7, 8], "root": 3, "rotat": [3, 6], "rout": 7, "rtx": [4, 8], "rule": [1, 2, 4, 6, 8], "run": [2, 4, 7], "safe": [3, 8], "safe_load": 6, "safeti": [1, 4, 6], "sampl": [1, 5, 8], "sanit": [7, 8], "sanitize_input": [3, 8], "scalabl": 2, "scale": [4, 7, 8], "scenario": 1, "score": [2, 8], "score_threshold": 2, "search": [7, 8], "second": [3, 8], "secret": 3, "section": [5, 8], "secur": [1, 8], "security_api_kei": 6, "security_block_dur": 6, "security_cors_origin": 6, "security_manag": 3, "security_max_failed_attempt": 6, "security_max_request_s": 6, "security_middlewar": 3, "security_rate_limit_request": 6, "security_rate_limit_window": 6, "securitymanag": [7, 8], "see": 7, "select": [2, 8], "self": 3, "semant": [2, 7], "sender": [1, 8], "sensibl": 1, "sensit": 6, "sent": [0, 8], "sentenc": [2, 8], "separ": [6, 7], "sequenc": [1, 5], "serial": [7, 8], "server": [0, 5, 6, 7, 8], "servic": [6, 8], "service_nam": 8, "serviceunavailableerror": 8, "set": [0, 2, 3, 4, 5, 7, 8], "set_context": 8, "setup": [2, 7, 8], "setup_log": 8, "setup_structured_log": 8, "setup_vector_db": [2, 8], "shape": 8, "share": [3, 5, 8], "should": [0, 1, 2, 8], "show_progress": 2, "shutdown": [0, 8], "shutdown_ev": [0, 7, 8], "signific": [2, 8], "significantli": [2, 4, 8], "similar": [2, 8], "simpl": [3, 4, 8], "singl": [1, 8], "size": [0, 1, 2, 4, 5, 6, 7, 8], "small": [2, 6], "smaller": 6, "smith": [1, 8], "snif": 3, "sonnet": [4, 6], "sophist": [2, 4, 8], "sort": [2, 8], "sourc": [0, 1, 2, 3, 4, 8], "spars": [2, 8], "specif": [2, 4, 8], "specifi": [3, 4, 8], "speed": [2, 8], "split": 1, "src": [6, 7], "stack": 7, "stage": [2, 6, 7, 8], "start": [6, 8], "start_request": 8, "start_tim": 8, "stat": 8, "statement": [4, 8], "statist": [0, 1, 8], "statu": [1, 4, 5, 8], "status_cod": [3, 8], "step": [4, 8], "stop": [1, 5, 8], "storag": 2, "store": [2, 3, 8], "str": [0, 1, 2, 3, 4, 8], "str_strip_whitespac": [1, 8], "stream": [0, 7, 8], "streamingrespons": [0, 8], "strict": 3, "string": [2, 4, 5, 8], "strip": 3, "strong": 3, "structur": [0, 1, 3, 4, 5, 6, 7, 8], "structured_log": 6, "structuredformatt": 8, "success": [0, 1, 2, 8], "suit": 7, "sum": [1, 8], "support": [0, 1, 3, 6, 8], "swagger": 5, "symbol": [0, 1, 2, 3, 5, 6, 8], "symbolic_ai_exception_handl": [0, 7, 8], "symbolic_reason": [4, 8], "symbolicai": [0, 8], "symbolicaiexcept": [0, 8], "symbolicreason": [7, 8], "syntax": 6, "system": [3, 4, 6, 7], "system_info": [0, 7, 8], "system_msg": 1, "systeminfo": [7, 8], "systemmetr": [1, 8], "t": [1, 8], "taken": [4, 8], "task": [4, 8], "temperatur": [0, 1, 4, 5, 8], "templat": 7, "temporarili": 8, "test": [2, 3, 5, 6, 7, 8], "text": [0, 1, 2, 3, 5, 8], "textbook": 2, "thi": [0, 1, 2, 3, 4, 5, 6, 7, 8], "thoroughli": 3, "thread": 8, "threshold": 2, "through": 6, "throughout": 1, "throughput": [1, 4], "time": [1, 3, 7, 8], "timeout": [6, 7], "timestamp": [1, 8], "titl": 6, "to_dict": [3, 8], "todai": [1, 8], "token": [1, 3, 4, 5, 7, 8], "tokenusag": [1, 7, 8], "too": [3, 8], "tool": 6, "top": [2, 8], "top_p": [1, 5, 8], "topic": [2, 8], "torch": 6, "torchvectorstor": 8, "total": [1, 8], "total_token": [1, 8], "trace": 8, "track": [2, 4, 6, 7, 8], "traffic": 3, "transform": 2, "transit": [4, 8], "transport": 3, "trigger": 3, "troubleshoot": 7, "true": [0, 1, 2, 3, 4, 5, 6, 8], "trust": 1, "try": [1, 2, 3, 4], "ttl": [2, 4, 7, 8], "ttl_second": 8, "turbo": [4, 6], "two": [2, 8], "txt": 7, "type": [0, 1, 2, 3, 4, 5, 6, 8], "typic": [2, 8], "u": 6, "ui": 5, "unavail": 8, "under": 7, "underli": [4, 8], "unexpect": 5, "unifi": 8, "union": [2, 8], "uniqu": [1, 2, 3, 8], "unix": [1, 8], "unless": [1, 8], "up": [2, 4, 6, 8], "updat": [2, 3], "upload": 3, "upload_endpoint": 3, "us": [0, 1, 2, 3, 4, 5, 6, 7, 8], "usag": [7, 8], "use_gpu": [2, 4, 6, 8], "user": [0, 1, 3, 5, 8], "user1": 6, "user2": 6, "user_input": 3, "user_msg": 1, "using_torch_fallback": [2, 8], "usual": 3, "util": [4, 7], "uvicorn": 6, "v": [1, 8], "v1": [0, 2, 6, 7], "v2": [1, 2, 8], "valid": [0, 2, 4, 7, 8], "validate_api_kei": [3, 8], "validate_assign": [1, 8], "validate_configur": 6, "validate_cont": [1, 8], "validate_cors_origin": [3, 7, 8], "validate_custom_set": 6, "validate_messag": [1, 8], "validate_temperatur": 1, "validate_text": [1, 8], "validate_total_token": [1, 8], "validation_error": [0, 1, 5], "validation_exception_handl": [0, 7, 8], "validationerror": [0, 1, 2, 4, 8], "valu": [0, 1, 2, 3, 6, 8], "valueerror": [1, 3, 8], "var": 6, "variabl": [3, 4, 7, 8], "variou": 4, "vector": [5, 7], "vector_db": [2, 8], "vector_db_backend": 6, "vector_stor": 8, "vectorstoreerror": [2, 8], "veri": 2, "verifi": [3, 8], "verify_api_kei": [3, 7, 8], "version": [1, 4, 6, 8], "via": [3, 4], "wa": [1, 8], "wait": 8, "warn": [6, 8], "we": [4, 8], "web": 7, "welcom": 7, "well": 2, "what": [0, 1, 2, 4, 7, 8], "when": [1, 3, 4, 6, 8], "where": [2, 5, 8], "whether": [1, 2, 4, 8], "while": [3, 8], "whitelist": 3, "whitespac": 3, "why": [1, 8], "wildcard": 3, "window": [3, 8], "within": [3, 4, 8], "work": [4, 7], "worker": [0, 6, 7], "world": 3, "would": [2, 4, 8], "wrapper": [2, 8], "x": [3, 5], "xss": 3, "you": [1, 5, 6, 8], "your": [0, 2, 5, 6, 7], "yourdomain": [3, 6, 8], "zip": 4}, "titles": ["Main Application Module", "Data Models Module", "Vector Retrieval Module", "Security Module", "Symbolic Reasoning Module", "API Reference", "Configuration Guide", "Neural Symbolic Language Model Documentation", "API Reference"], "titleterms": {"acceler": 2, "advanc": [2, 4], "anthrop": 4, "api": [3, 5, 7, 8], "applic": [0, 6], "architectur": 7, "attempt": 3, "authent": [3, 5], "backend": 2, "base": 5, "basic": [2, 4], "batch": [2, 4], "best": [1, 2, 3, 6], "block": 3, "built": 6, "cach": [2, 4, 6], "chat": [0, 5, 8], "chatcompletionchunk": 1, "chatcompletionrequest": 1, "chatcompletionrespons": 1, "check": [0, 5], "choic": 1, "choicedelta": 1, "chromadb": 2, "class": [2, 3, 4], "client": 3, "code": 5, "common": [5, 6], "compat": 5, "complet": [0, 1, 3, 5], "compon": [7, 8], "configur": [0, 2, 3, 4, 6, 8], "consider": [2, 4], "content": 7, "convers": 1, "cor": [3, 5], "core": [2, 3, 4, 6, 7, 8], "custom": [1, 6], "cycl": 1, "data": 1, "default": 5, "deploy": 7, "design": 1, "detect": 3, "develop": [6, 7], "dictionari": 1, "docker": 6, "document": [2, 5, 7], "embed": 2, "endpoint": [0, 5, 8], "engin": 4, "environ": 6, "error": [0, 1, 2, 3, 4, 5], "errordetail": 1, "errorrespons": 1, "exampl": [0, 1, 2, 3, 4], "except": 8, "fail": 3, "faiss": 2, "fastapi": 0, "featur": [1, 7], "field": 1, "file": 6, "format": 5, "gener": 6, "gpu": [2, 4], "guid": [6, 7], "handl": [0, 2, 3, 4, 8], "header": [3, 5], "health": [0, 5], "index": 2, "indic": 7, "info": 5, "inform": [0, 2, 5], "initi": [2, 3, 4], "input": 3, "ip": 3, "issu": 6, "json": 1, "kei": 3, "languag": 7, "licens": 7, "limit": [3, 5], "load": 6, "local": 4, "log": [6, 8], "main": 0, "manag": [3, 6, 8], "messag": [1, 5], "method": [2, 3, 4], "metric": 5, "model": [1, 2, 6, 7], "modul": [0, 1, 2, 3, 4, 8], "monitor": [0, 5, 6, 7, 8], "neural": 7, "observ": 7, "openai": [4, 5], "openapi": 5, "optim": 2, "origin": 3, "overview": [1, 2, 3, 4, 6, 7], "paramet": 5, "perform": [0, 1, 2, 4, 5, 6, 7], "performancemetr": 1, "practic": [1, 2, 3, 6], "prepar": 2, "process": [2, 4], "product": [6, 7], "queri": 2, "quick": 7, "rate": [3, 5], "ratelimit": 3, "readi": 5, "reason": 4, "refer": [5, 7, 8], "request": [1, 3, 5], "respons": [1, 5], "rest": 5, "retriev": 2, "rout": 8, "sanit": 3, "scale": 2, "search": 2, "secur": [0, 3, 6, 7], "securitymanag": 3, "serial": 1, "set": 6, "setup": [3, 6], "size": 3, "specif": 6, "start": 7, "storag": 8, "stream": [1, 5], "support": [2, 4, 5, 7], "symbol": [4, 7], "symbolicreason": 4, "system": [0, 1, 2, 5, 8], "systeminfo": 1, "tabl": 7, "templat": 6, "track": 3, "troubleshoot": 6, "url": 5, "usag": [0, 1, 2, 3, 4], "user": 7, "util": 8, "valid": [1, 3, 6], "variabl": 6, "vector": [2, 8], "yaml": 6}})