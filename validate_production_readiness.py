#!/usr/bin/env python3
"""
Production Readiness Validation Script

This script validates that the Neural Symbolic Language Model is production-ready
by checking code structure, configuration, security, and other critical aspects.
"""

import os
import sys
import json
import importlib.util
from pathlib import Path
from typing import List, Dict, Any
import ast


class ProductionReadinessValidator:
    """Validates production readiness of the codebase."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.issues = []
        self.warnings = []
        self.passed_checks = []
        
    def log_issue(self, category: str, message: str):
        """Log a critical issue."""
        self.issues.append(f"[{category}] {message}")
        
    def log_warning(self, category: str, message: str):
        """Log a warning."""
        self.warnings.append(f"[{category}] {message}")
        
    def log_pass(self, category: str, message: str):
        """Log a passed check."""
        self.passed_checks.append(f"[{category}] {message}")
    
    def check_file_structure(self):
        """Check that required files and directories exist."""
        print("🔍 Checking file structure...")
        
        required_files = [
            "src/main.py",
            "src/symbolic_reasoning.py", 
            "src/retrieval.py",
            "src/security.py",
            "src/exceptions.py",
            "src/models.py",
            "src/core/config.py",
            "src/core/cache.py",
            "src/logging_config.py",
            "requirements.txt",
            "pytest.ini",
            ".env.example",
            "README.md"
        ]
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.log_pass("STRUCTURE", f"Required file exists: {file_path}")
            else:
                self.log_issue("STRUCTURE", f"Missing required file: {file_path}")
        
        required_dirs = [
            "src",
            "src/api",
            "src/api/routes", 
            "src/core",
            "tests",
            "docs",
            "config"
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                self.log_pass("STRUCTURE", f"Required directory exists: {dir_path}")
            else:
                self.log_issue("STRUCTURE", f"Missing required directory: {dir_path}")
    
    def check_configuration(self):
        """Check configuration management."""
        print("⚙️ Checking configuration...")
        
        # Check config.py exists and has required classes
        config_path = self.project_root / "src/core/config.py"
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                required_classes = [
                    "SecuritySettings", "ModelSettings", "CacheSettings", 
                    "LoggingSettings", "AppSettings"
                ]
                
                for class_name in required_classes:
                    if f"class {class_name}" in content:
                        self.log_pass("CONFIG", f"Configuration class found: {class_name}")
                    else:
                        self.log_warning("CONFIG", f"Configuration class missing: {class_name}")
                
                # Check for BaseSettings usage
                if "BaseSettings" in content:
                    self.log_pass("CONFIG", "Using Pydantic BaseSettings")
                else:
                    self.log_issue("CONFIG", "Not using Pydantic BaseSettings")
                    
            except Exception as e:
                self.log_issue("CONFIG", f"Error reading config.py: {str(e)}")
        
        # Check environment template
        env_example = self.project_root / ".env.example"
        if env_example.exists():
            self.log_pass("CONFIG", "Environment template exists")
        else:
            self.log_issue("CONFIG", "Missing .env.example template")
    
    def check_security(self):
        """Check security implementation."""
        print("🔒 Checking security...")
        
        security_path = self.project_root / "src/security.py"
        if security_path.exists():
            try:
                with open(security_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                security_features = [
                    ("API key authentication", "api_key"),
                    ("Rate limiting", "rate_limit"),
                    ("Input sanitization", "sanitize"),
                    ("CORS configuration", "cors"),
                    ("Security headers", "security_headers")
                ]
                
                for feature_name, keyword in security_features:
                    if keyword.lower() in content.lower():
                        self.log_pass("SECURITY", f"Security feature implemented: {feature_name}")
                    else:
                        self.log_warning("SECURITY", f"Security feature missing: {feature_name}")
                        
            except Exception as e:
                self.log_issue("SECURITY", f"Error reading security.py: {str(e)}")
        else:
            self.log_issue("SECURITY", "Missing security.py module")
    
    def check_error_handling(self):
        """Check error handling implementation."""
        print("🚨 Checking error handling...")
        
        exceptions_path = self.project_root / "src/exceptions.py"
        if exceptions_path.exists():
            try:
                with open(exceptions_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Parse AST to find exception classes
                tree = ast.parse(content)
                exception_classes = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        # Check if it inherits from Exception or a subclass
                        for base in node.bases:
                            if isinstance(base, ast.Name) and 'Error' in base.id:
                                exception_classes.append(node.name)
                                break
                
                if exception_classes:
                    self.log_pass("ERROR_HANDLING", f"Custom exceptions defined: {', '.join(exception_classes)}")
                else:
                    self.log_warning("ERROR_HANDLING", "No custom exception classes found")
                    
            except Exception as e:
                self.log_issue("ERROR_HANDLING", f"Error analyzing exceptions.py: {str(e)}")
        else:
            self.log_issue("ERROR_HANDLING", "Missing exceptions.py module")
    
    def check_testing(self):
        """Check testing infrastructure."""
        print("🧪 Checking testing infrastructure...")
        
        # Check pytest configuration
        pytest_ini = self.project_root / "pytest.ini"
        if pytest_ini.exists():
            self.log_pass("TESTING", "pytest.ini configuration exists")
        else:
            self.log_warning("TESTING", "Missing pytest.ini configuration")
        
        # Check test files
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            test_files = list(tests_dir.glob("test_*.py"))
            if test_files:
                self.log_pass("TESTING", f"Found {len(test_files)} test files")
                for test_file in test_files:
                    self.log_pass("TESTING", f"Test file: {test_file.name}")
            else:
                self.log_issue("TESTING", "No test files found in tests directory")
        else:
            self.log_issue("TESTING", "Missing tests directory")
        
        # Check conftest.py
        conftest = self.project_root / "tests/conftest.py"
        if conftest.exists():
            self.log_pass("TESTING", "Test fixtures configuration exists")
        else:
            self.log_warning("TESTING", "Missing conftest.py test fixtures")
    
    def check_documentation(self):
        """Check documentation."""
        print("📚 Checking documentation...")
        
        # Check README
        readme = self.project_root / "README.md"
        if readme.exists():
            self.log_pass("DOCS", "README.md exists")
        else:
            self.log_issue("DOCS", "Missing README.md")
        
        # Check Sphinx configuration
        sphinx_conf = self.project_root / "docs/conf.py"
        if sphinx_conf.exists():
            self.log_pass("DOCS", "Sphinx documentation configuration exists")
        else:
            self.log_warning("DOCS", "Missing Sphinx documentation configuration")
    
    def check_dependencies(self):
        """Check dependency management."""
        print("📦 Checking dependencies...")
        
        # Check requirements.txt
        requirements = self.project_root / "requirements.txt"
        if requirements.exists():
            try:
                with open(requirements, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                critical_deps = [
                    "fastapi", "uvicorn", "pydantic", "torch", "numpy"
                ]
                
                for dep in critical_deps:
                    if dep in content.lower():
                        self.log_pass("DEPS", f"Critical dependency found: {dep}")
                    else:
                        self.log_warning("DEPS", f"Critical dependency missing: {dep}")
                        
            except Exception as e:
                self.log_issue("DEPS", f"Error reading requirements.txt: {str(e)}")
        else:
            self.log_issue("DEPS", "Missing requirements.txt")
        
        # Check pyproject.toml
        pyproject = self.project_root / "pyproject.toml"
        if pyproject.exists():
            self.log_pass("DEPS", "Modern Python project configuration exists")
        else:
            self.log_warning("DEPS", "Missing pyproject.toml configuration")
    
    def run_validation(self) -> Dict[str, Any]:
        """Run all validation checks."""
        print("🚀 Starting Production Readiness Validation...\n")
        
        self.check_file_structure()
        self.check_configuration()
        self.check_security()
        self.check_error_handling()
        self.check_testing()
        self.check_documentation()
        self.check_dependencies()
        
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate validation report."""
        total_checks = len(self.passed_checks) + len(self.warnings) + len(self.issues)
        
        report = {
            "summary": {
                "total_checks": total_checks,
                "passed": len(self.passed_checks),
                "warnings": len(self.warnings),
                "issues": len(self.issues),
                "production_ready": len(self.issues) == 0
            },
            "passed_checks": self.passed_checks,
            "warnings": self.warnings,
            "issues": self.issues
        }
        
        return report


def main():
    """Main validation function."""
    validator = ProductionReadinessValidator()
    report = validator.run_validation()
    
    print("\n" + "="*80)
    print("🎯 PRODUCTION READINESS VALIDATION REPORT")
    print("="*80)
    
    summary = report["summary"]
    print(f"📊 Total Checks: {summary['total_checks']}")
    print(f"✅ Passed: {summary['passed']}")
    print(f"⚠️  Warnings: {summary['warnings']}")
    print(f"❌ Issues: {summary['issues']}")
    
    if summary["production_ready"]:
        print("\n🎉 PRODUCTION READY! ✅")
    else:
        print("\n⚠️  NOT PRODUCTION READY - Issues found ❌")
    
    if report["issues"]:
        print("\n❌ CRITICAL ISSUES:")
        for issue in report["issues"]:
            print(f"  • {issue}")
    
    if report["warnings"]:
        print("\n⚠️  WARNINGS:")
        for warning in report["warnings"]:
            print(f"  • {warning}")
    
    print(f"\n✅ PASSED CHECKS ({len(report['passed_checks'])}):")
    for check in report["passed_checks"]:
        print(f"  • {check}")
    
    return 0 if summary["production_ready"] else 1


if __name__ == "__main__":
    sys.exit(main())
