"""
Main application module for the Neural Symbolic Language Model.
This module provides the FastAPI server implementation and core API endpoints.
"""

# Import required modules
import os
import json
import uuid
import time
from typing import Dict, List, Optional
from document_loader import DocumentLoader

# Import logging configuration
from logging_config import setup_logging, get_logger
logger = get_logger(__name__)

try:
    import faiss
    FAISS_AVAILABLE = True
    logger.info("Imported FAISS successfully")
    
    # Check if GPU FAISS is available
    GPU_FAISS_AVAILABLE = hasattr(faiss, 'StandardGpuResources')
    if GPU_FAISS_AVAILABLE:
        logger.info(f"GPU FAISS is available")
    else:
        logger.info("Using CPU version of FAISS")
        
except ImportError:
    logger.warning("FAISS not available. Using fallback vector storage.")
    FAISS_AVAILABLE = False
    GPU_FAISS_AVAILABLE = False
    faiss = None

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.responses import StreamingResponse, FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import os

class DocumentAddRequest(BaseModel):
    text: str
    metadata: Optional[Dict] = None

class DocumentAddResponse(BaseModel):
    success: bool
    message: str
    document_id: Optional[str] = None
from datetime import datetime
import asyncio

# OpenAI compatible models
class OpenAIMessage(BaseModel):
    role: str
    content: str

class OpenAIChatRequest(BaseModel):
    model: str
    messages: List[OpenAIMessage]
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    stream: bool = False

class OpenAIChatChoice(BaseModel):
    index: int
    message: OpenAIMessage
    finish_reason: str

class OpenAIChatResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[OpenAIChatChoice]
    usage: Dict
import time
import torch
import uvicorn

# Import local modules
from symbolic_reasoning import SymbolicReasoner
from retrieval import Retriever
from logging_config import setup_logging, get_logger
from monitoring import PerformanceMonitor

# Set up logging
setup_logging()
logger = get_logger(__name__)

# Initialize performance monitoring
monitor = PerformanceMonitor()

# Create FastAPI app
app = FastAPI(
    title="SymbolicAI + LightRAG GPT-4o Alternative",
    description="""A local GPT-4o alternative using SymbolicAI and LightRAG.
    
    Available endpoints:
    - GET /: Web interface
    - GET /docs: Interactive API documentation
    - GET /redoc: ReDoc API documentation
    - POST /chat: Send a chat message
    - GET /system/info: Get system information
    """,
    version="0.1.0"
)

# Set up templates and static files
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
static_dir = os.path.join(BASE_DIR, 'static')
app.mount("/static", StaticFiles(directory=static_dir), name="static")

templates = Jinja2Templates(directory=os.path.join(BASE_DIR, 'templates'))

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    # Serve the main web interface.
    return FileResponse(os.path.join(static_dir, 'index.html'))

@app.get("/api/info")
async def api_info():
    """Get API information and available endpoints."""
    return {
        "name": "Neural Symbolic Language Model API",
        "version": "0.1.0",
        "description": "A local GPT-4o alternative using SymbolicAI and LightRAG",
        "endpoints": {
            "root": {
                "path": "/",
                "method": "GET",
                "description": "Web interface"
            },
            "api_info": {
                "path": "/api/info",
                "method": "GET",
                "description": "This API documentation"
            },
            "docs": {
                "path": "/docs",
                "method": "GET",
                "description": "Interactive API documentation (Swagger UI)"
            },
            "redoc": {
                "path": "/redoc",
                "method": "GET",
                "description": "ReDoc API documentation"
            },
            "chat": {
                "path": "/chat",
                "method": "POST",
                "description": "Send a chat message"
            },
            "system_info": {
                "path": "/system/info",
                "method": "GET",
                "description": "Get system information"
            }
        }
    }

# Initialize components
logger.info("Initializing components...")
# Check GPU and FAISS availability
gpu_available = torch.cuda.is_available()
gpu_faiss_available = GPU_FAISS_AVAILABLE if 'GPU_FAISS_AVAILABLE' in globals() else False

if gpu_available:
    logger.info(f"GPU available: {torch.cuda.get_device_name(0)}")
    if not gpu_faiss_available:
        logger.warning("GPU FAISS not available. Will use CPU version of FAISS.")
else:
    logger.warning("GPU not available. Running in CPU mode.")

# Initialize components with appropriate GPU settings
use_gpu = gpu_available and (not 'FAISS_AVAILABLE' in globals() or FAISS_AVAILABLE)
reasoner = SymbolicReasoner(use_gpu=use_gpu)
retriever = Retriever(use_gpu=use_gpu and gpu_faiss_available)
logger.info("Components initialized successfully")

# Load documents from data directory (if exists)
data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
if os.path.exists(data_dir):
    logger.info(f"Loading documents from {data_dir}")
    # Load documents with supported extensions
    documents_data = DocumentLoader.load_directory(
        data_dir, 
        extensions=['.txt', '.md']
    )
    
    # Add documents to retriever
    documents = list(documents_data.values())
    if documents:
        logger.info(f"Loading {len(documents)} documents from data directory")
        retriever.add_documents(documents)
        logger.info("Documents loaded successfully")
    else:
        logger.warning("No documents found in data directory")

# Create advanced response cache
class ResponseCache:
    def __init__(self, max_size=1000):
        self.cache = {}
        self.timestamps = {}
        self.max_size = max_size
    
    def get(self, key):
        """Get a value from the cache.
        
        Args:
            key (str): The cache key
            
        Returns:
            The cached value, or None if not found
        """
        if key in self.cache:
            # Update access timestamp
            self.timestamps[key] = time.time()
            return self.cache[key]
        return None
    
    def set(self, key, value):
        """Set a value in the cache.
        
        Args:
            key (str): The cache key
            value: The value to cache
        """
        # Clean cache if needed
        if len(self.cache) >= self.max_size:
            self.clean(int(self.max_size * 0.2))  # Remove 20% of entries
        
        # Store value and timestamp
        self.cache[key] = value
        self.timestamps[key] = time.time()
    
    def clean(self, count=None):
        """Clean old entries from the cache.
        
        Args:
            count (int, optional): Number of entries to remove
        """
        if not self.cache:
            return
        
        # Sort keys by timestamp
        sorted_keys = sorted(self.timestamps.keys(), key=lambda k: self.timestamps[k])
        
        # Determine how many entries to remove
        remove_count = count if count is not None else len(sorted_keys) // 2
        
        # Remove oldest entries
        for key in sorted_keys[:remove_count]:
            del self.cache[key]
            del self.timestamps[key]
    
    def size(self):
        """Get the current cache size.
        
        Returns:
            int: The number of entries in the cache
        """
        return len(self.cache)

# Initialize improved cache
response_cache = ResponseCache(max_size=1000)

# Define request/response models for API
class ChatRequest(BaseModel):
    text: str

class ChatResponse(BaseModel):
    response: str
    cached: bool = False

# Optimize GPU memory usage
def optimize_gpu_memory():
    """Configure PyTorch for optimal GPU memory usage"""
    if torch.cuda.is_available():
        # Empty cache before processing
        torch.cuda.empty_cache()
        
        # Set memory allocation strategy
        torch.cuda.set_per_process_memory_fraction(0.8)  # Use 80% of available GPU memory
        
        # Enable memory optimization
        torch.backends.cudnn.benchmark = True
        
        return True
    return False

# Call GPU optimization at startup
optimize_gpu_memory()

# Add sample documents for testing
sample_documents = [
    "Neural-Symbolic AI integrates logic-based reasoning with deep learning models.",
    "FAISS is a library for efficient similarity search developed by Facebook AI.",
    "Retrieval-Augmented Generation improves language model outputs by incorporating external knowledge.",
    "GPT models are transformer-based language models developed by OpenAI.",
    "Vector databases store and retrieve high-dimensional vectors for similarity search."
]

# Add documents to retriever
documents_with_ids = [{'id': str(i), 'text': doc} for i, doc in enumerate(sample_documents)]
retriever.add_documents(documents_with_ids)

# Helper function to process queries
async def process_query(query: str) -> str:
    """Process a query using both retrieval and reasoning components.
    
    Args:
        query: The user's query text
        
    Returns:
        str: The generated response
    """
    # First, retrieve relevant information
    retrieved_info = retriever.search(query, k=2)
    
    # Format retrieved information as context
    context = "\n".join([f"Context {i+1}: {result['text']}" for i, result in enumerate(retrieved_info)])
    
    # Use symbolic reasoner to process query with context
    response = reasoner.process_query(query, context=context)
    
    return response

# API endpoints
@app.post("/documents/add", response_model=DocumentAddResponse)
async def add_document(request: DocumentAddRequest):
    """Add a document to the retrieval system.
    
    Args:
        request: DocumentAddRequest object containing the document content
        
    Returns:
        DocumentAddResponse object indicating success
    """
    try:
        # Add document to retriever
        retriever.add_documents([{'text': request.content}])
        
        return DocumentAddResponse(
            success=True,
            message="Document added successfully"
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error adding document: {str(e)}")

@app.get("/documents/count")
async def get_document_count():
    """Get the number of documents in the retrieval system.
    
    Returns:
        dict: Number of documents
    """
    # This is a placeholder - actual implementation depends on LightRAG
    # Assuming retriever has a `document_count` method
    if hasattr(retriever, 'document_count'):
        count = retriever.document_count()
    else:
        count = -1  # Unknown if not supported
        
    return {"count": count}

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, background_tasks: BackgroundTasks):
    """Process a chat request.
    
    Args:
        request: ChatRequest object containing the text query
        background_tasks: FastAPI BackgroundTasks for async operations
        
    Returns:
        ChatResponse: Response from the system
    """
    return await process_chat_request(request, background_tasks)

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """Process a chat request with streaming response.
    
    Args:
        request: ChatRequest object containing the text query
        
    Returns:
        StreamingResponse: Streaming response generator
    """
    # Check cache for identical queries
    cached_response = response_cache.get(request.text)
    if cached_response:
        # For cached responses, stream the entire response at once
        async def cached_generator():
            yield f"data: {json.dumps({'response': cached_response, 'cached': True})}\n\n"
            yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            cached_generator(),
            media_type="text/event-stream"
        )
    
    try:
        # Perform retrieval
        retrieved_info = await asyncio.to_thread(retriever.query, request.text)
        
        # Combine input with retrieved context
        enriched_query = f"{request.text}"
        if retrieved_info:
            enriched_query += f"\n\nAdditional context: {retrieved_info}"
        
        # This is a placeholder for streaming - actual implementation depends on SymbolicAI
        # Assuming reasoner has a `process_query_stream` method
        async def generator():
            if hasattr(reasoner, 'process_query_stream'):
                # Use streaming if available
                async for chunk in reasoner.process_query_stream(enriched_query):
                    yield f"data: {json.dumps({'response': chunk, 'finished': False})}\n\n"
            else:
                # Fall back to non-streaming
                response = await asyncio.to_thread(reasoner.process_query, enriched_query)
                yield f"data: {json.dumps({'response': response, 'finished': True})}\n\n"
            
            # Signal completion
            yield "data: [DONE]\n\n"
            
            # Cache the complete response
            if not hasattr(reasoner, 'process_query_stream'):
                response_cache.set(request.text, response)
        
        return StreamingResponse(
            generator(),
            media_type="text/event-stream"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")
    """Process a chat request.
    
    Args:
        request: The chat request containing the user's query
        background_tasks: FastAPI background tasks handler
        
    Returns:
        ChatResponse: The response to the user's query
    """
    query = request.text
    
    # Check cache first
    cached_response = response_cache.get(query)
    if cached_response:
        return ChatResponse(
            response=cached_response,
            cached=True
        )
    
    try:
        # Process the query
        response = await process_query(query)
        
        # Cache the response in the background
        background_tasks.add_task(lambda: response_cache.set(query, response))
        
        return ChatResponse(
            response=response,
            cached=False
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def clean_cache():
    """Clean old entries from the response cache.
    
    Keeps the cache size manageable by removing oldest entries.
    """
    # Keep cache size manageable
    if len(response_cache) > 100:
        # Remove oldest entries
        for key in list(response_cache.keys())[:50]:
            del response_cache[key]

async def openai_chat_stream(request: OpenAIChatRequest):
    """Handle streaming for OpenAI-compatible chat endpoint.
    
    Args:
        request: OpenAIChatRequest object
        
    Returns:
        StreamingResponse: Server-sent events stream
    """
    logger.info(f"Received streaming chat request for model: {request.model}")
    
    # Extract the last user message
    last_message = None
    for msg in reversed(request.messages):
        if msg.role == "user":
            last_message = msg.content
            break
    
    if not last_message:
        logger.warning("No user message found in streaming request")
        raise HTTPException(status_code=400, detail="No user message found")
    
    logger.debug(f"Processing streaming message: {last_message[:100]}...")
    
    # Check cache for identical queries
    cached_response = response_cache.get(last_message)
    if cached_response:
        logger.info("Found response in cache for streaming request")
        async def cached_generator():
            yield json.dumps({
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "delta": {
                        "role": "assistant",
                        "content": cached_response
                    },
                    "finish_reason": None
                }]
            }) + "\n"
            
            # Send completion message
            yield json.dumps({
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            }) + "\n"
        
        return StreamingResponse(
            cached_generator(),
            media_type="text/event-stream"
        )
    
    try:
        # Perform retrieval
        logger.info("Performing retrieval operation for streaming request")
        search_results = await asyncio.to_thread(retriever.search, last_message)
        
        # Extract text from search results
        retrieved_info = "\n".join([result['text'] for result in search_results]) if search_results else ""
        
        # Combine input with retrieved context
        enriched_query = f"{last_message}"
        if retrieved_info:
            logger.info("Retrieved relevant context for streaming request")
            enriched_query += f"\n\nAdditional context: {retrieved_info}"
        else:
            logger.info("No relevant context found for streaming request")
        
        async def generator():
            if hasattr(reasoner, 'process_query_stream'):
                # Use streaming if available
                logger.info("Using native streaming capability")
                async for chunk in reasoner.process_query_stream(enriched_query):
                    logger.debug(f"Streaming chunk: {chunk[:50]}...")
                    yield json.dumps({
                        "id": f"chatcmpl-{uuid.uuid4()}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": request.model,
                        "choices": [{
                            "index": 0,
                            "delta": {
                                "role": "assistant",
                                "content": chunk
                            },
                            "finish_reason": None
                        }]
                    }) + "\n"
            else:
                # Fall back to non-streaming
                logger.info("Native streaming not available, falling back to single response")
                response = await asyncio.to_thread(reasoner.process_query, enriched_query)
                logger.debug(f"Generated full response: {response[:100]}...")
                yield json.dumps({
                    "id": f"chatcmpl-{uuid.uuid4()}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": request.model,
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "role": "assistant",
                            "content": response
                        },
                        "finish_reason": None
                    }]
                }) + "\n"
            
            # Send completion message
            logger.debug("Sending completion message")
            yield json.dumps({
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            }) + "\n"
            
            # Cache the complete response if not using streaming
            if not hasattr(reasoner, 'process_query_stream'):
                logger.debug("Caching non-streaming response")
                response_cache.set(last_message, response)
            
            logger.info("Streaming response completed successfully")
        
        return StreamingResponse(
            generator(),
            media_type="text/event-stream"
        )
        
    except Exception as e:
        logger.error(f"Error processing streaming request: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")

@app.post("/v1/chat/completions", response_model=OpenAIChatResponse)
async def openai_chat(request: OpenAIChatRequest):
    """OpenAI-compatible chat endpoint.
    
    Args:
        request: OpenAIChatRequest object
        
    Returns:
        OpenAIChatResponse object or StreamingResponse if streaming
    """
    request_id = f"chat-{uuid.uuid4()}"
    monitor.start_request(request_id, "/v1/chat/completions")
    logger.info(f"Received chat request for model: {request.model}")
    
    # Handle streaming requests
    if request.stream:
        logger.info("Request requires streaming, forwarding to streaming endpoint")
        monitor.end_request(request_id)
        return await openai_chat_stream(request)
    
    # Extract the last user message
    last_message = None
    for msg in reversed(request.messages):
        if msg.role == "user":
            last_message = msg.content
            break
    
    if not last_message:
        logger.warning("No user message found in request")
        monitor.end_request(request_id, error="No user message found")
        raise HTTPException(status_code=400, detail="No user message found")
    
    logger.debug(f"Processing message: {last_message[:100]}...")
    
    # Check cache for identical queries
    cached_response = response_cache.get(last_message)
    if cached_response:
        logger.info("Found response in cache")
        monitor.record_cache_hit(request_id)
        response_obj = OpenAIChatResponse(
            id=f"chatcmpl-{uuid.uuid4()}",
            created=int(time.time()),
            model=request.model,
            choices=[
                OpenAIChatChoice(
                    index=0,
                    message=OpenAIMessage(
                        role="assistant",
                        content=cached_response
                    ),
                    finish_reason="stop"
                )
            ],
            usage={
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        )
        monitor.end_request(request_id)
        return response_obj
    
    monitor.record_cache_miss(request_id)
    
    try:
        # Perform retrieval
        logger.info("Performing retrieval operation")
        retrieval_start = time.time()
        search_results = await asyncio.to_thread(retriever.search, last_message)
        monitor.record_retrieval_time(request_id, time.time() - retrieval_start)
        
        # Extract text from search results
        retrieved_info = "\n".join([result['text'] for result in search_results]) if search_results else ""
        
        # Combine input with retrieved context
        enriched_query = f"{last_message}"
        if retrieved_info:
            logger.info("Retrieved relevant context")
            enriched_query += f"\n\nAdditional context: {retrieved_info}"
        else:
            logger.info("No relevant context found")
        
        # Process with symbolic reasoning
        logger.info("Processing with symbolic reasoning")
        reasoning_start = time.time()
        response = await asyncio.to_thread(reasoner.process_query, enriched_query)
        monitor.record_reasoning_time(request_id, time.time() - reasoning_start)
        logger.info("Symbolic reasoning completed")
        
        # Cache the response for future use
        logger.debug("Caching response")
        response_cache.set(last_message, response)
        
        # Calculate token counts
        total_tokens = len(enriched_query.split()) + len(response.split())
        monitor.record_token_count(request_id, total_tokens)
        
        response_obj = OpenAIChatResponse(
            id=f"chatcmpl-{uuid.uuid4()}",
            created=int(time.time()),
            model=request.model,
            choices=[
                OpenAIChatChoice(
                    index=0,
                    message=OpenAIMessage(
                        role="assistant",
                        content=response
                    ),
                    finish_reason="stop"
                )
            ],
            usage={
                "prompt_tokens": len(enriched_query.split()),
                "completion_tokens": len(response.split()),
                "total_tokens": total_tokens
            }
        )
        
        logger.info("Successfully generated response")
        monitor.end_request(request_id)
        return response_obj
    
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}", exc_info=True)
        monitor.end_request(request_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")

@app.get("/performance")
async def performance_stats():
    """Get performance statistics for the system.
    
    Returns:
        dict: Performance statistics
    """
    logger.info("Retrieving performance statistics")
    try:
        # Get cache info
        cache_size = response_cache.size()
        logger.debug(f"Current cache size: {cache_size}")
        
        # Get GPU info
        gpu_available = torch.cuda.is_available()
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else None
        logger.debug(f"GPU status - Available: {gpu_available}, Name: {gpu_name}")
        
        # Get monitoring metrics
        monitoring_metrics = monitor.get_recent_metrics(minutes=5)
        
        stats = {
            "cache": {
                "size": cache_size,
                "max_size": response_cache.max_size,
                "hits": monitoring_metrics["cache"]["hits"],
                "misses": monitoring_metrics["cache"]["misses"],
                "hit_rate": monitoring_metrics["cache"]["hit_rate"]
            },
            "system": {
                "gpu_available": gpu_available,
                "gpu_name": gpu_name,
                "cpu_percent": monitoring_metrics["system"]["cpu_percent"],
                "memory_percent": monitoring_metrics["system"]["memory_percent"],
                "active_requests": monitoring_metrics["system"]["active_requests"],
                "reasoner": reasoner.get_system_info(),
                "retriever": retriever.get_system_info()
            },
            "requests": {
                "total": monitoring_metrics["requests"]["total"],
                "avg_duration": monitoring_metrics["requests"]["avg_duration"],
                "error_rate": monitoring_metrics["requests"]["error_rate"],
                "cache_hit_rate": monitoring_metrics["requests"]["cache_hit_rate"]
            }
        }
        
        logger.info("Successfully retrieved performance statistics")
        return stats
    except Exception as e:
        logger.error(f"Error retrieving performance statistics: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system/info")
async def system_info():
    """Get system configuration information."""
    logger.info("Retrieving system configuration information")
    try:
        gpu_optimized = torch.cuda.is_available() and torch.backends.cudnn.benchmark
        logger.debug(f"GPU optimization status: {gpu_optimized}")
        
        reasoner_info = reasoner.get_system_info()
        logger.debug("Retrieved reasoner system info")
        
        retriever_info = retriever.get_system_info()
        logger.debug("Retrieved retriever system info")
        
        info = {
            "reasoner": reasoner_info,
            "retriever": retriever_info,
            "gpu_optimized": gpu_optimized
        }
        
        logger.info("Successfully retrieved system configuration")
        return info
    except Exception as e:
        logger.error(f"Error retrieving system configuration: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.on_event("shutdown")
async def shutdown_event():
    """Perform cleanup on shutdown."""
    logger.info("Application shutting down")
    monitor.shutdown()

if __name__ == "__main__":
    # Run the API with development settings
    uvicorn.run(
        "main:app",    # Import string format
        host="127.0.0.1",  # Local connections only for development
        port=8080,        # Alternative port
        reload=True       # Enable auto-reload during development
    )
