# Core components
symbolicai
lightrag

# API and web server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
slowapi>=0.1.9
python-multipart>=0.0.6

# Vector database
# Note: Using faiss-cpu temporarily, will switch to faiss-gpu when installation issues are resolved
faiss-cpu
chromadb

# GPU acceleration
torch
torchvision

# Testing and development
pytest
matplotlib
numpy
requests

# Documentation
mkdocs
