"""
Security module for the Neural Symbolic Language Model.

This module provides authentication, authorization, rate limiting, and other
security features for the FastAPI application.

Author: AI Assistant
Date: 2025-06-29
"""

import hmac
import time
from typing import Dict, Optional
import logging

from fastapi import HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import secrets

# Configure logger
logger = logging.getLogger(__name__)

# Security configuration
API_KEY_HEADER = "X-API-Key"
DEFAULT_RATE_LIMIT = "100/minute"
MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB

class SecurityManager:
    """Manages security features for the application."""

    def __init__(self, api_keys: Optional[Dict[str, str]] = None):
        """Initialize the security manager.

        Args:
            api_keys: Dictionary mapping API key names to their values
        """
        self.api_keys = api_keys or {}
        self.failed_attempts: Dict[str, list] = {}
        self.blocked_ips: Dict[str, float] = {}

        # Generate a default API key if none provided
        if not self.api_keys:
            default_key = secrets.token_urlsafe(32)
            self.api_keys["default"] = default_key
            logger.warning(f"Generated default API key: {default_key}")

    def validate_api_key(self, api_key: str) -> bool:
        """Validate an API key.

        Args:
            api_key: The API key to validate

        Returns:
            True if valid, False otherwise
        """
        if not api_key:
            return False

        # Use constant-time comparison to prevent timing attacks
        for stored_key in self.api_keys.values():
            if hmac.compare_digest(api_key, stored_key):
                return True
        return False

    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if an IP address is blocked.

        Args:
            ip_address: The IP address to check

        Returns:
            True if blocked, False otherwise
        """
        if ip_address in self.blocked_ips:
            block_time = self.blocked_ips[ip_address]
            if time.time() - block_time < 3600:  # 1 hour block
                return True
            else:
                # Unblock after timeout
                del self.blocked_ips[ip_address]
        return False

    def record_failed_attempt(self, ip_address: str) -> None:
        """Record a failed authentication attempt.

        Args:
            ip_address: The IP address that failed authentication
        """
        current_time = time.time()

        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = []

        # Clean old attempts (older than 15 minutes)
        self.failed_attempts[ip_address] = [
            attempt_time for attempt_time in self.failed_attempts[ip_address]
            if current_time - attempt_time < 900
        ]

        # Add current attempt
        self.failed_attempts[ip_address].append(current_time)

        # Block IP if too many failed attempts
        if len(self.failed_attempts[ip_address]) >= 5:
            self.blocked_ips[ip_address] = current_time
            logger.warning(f"Blocked IP {ip_address} due to repeated failed attempts")

    def sanitize_input(self, text: str, max_length: int = 10000) -> str:
        """Sanitize user input text.

        Args:
            text: The input text to sanitize
            max_length: Maximum allowed length

        Returns:
            Sanitized text

        Raises:
            ValueError: If input is invalid
        """
        if not isinstance(text, str):
            raise ValueError("Input must be a string")

        if len(text) > max_length:
            raise ValueError(f"Input too long (max {max_length} characters)")

        # Remove null bytes and control characters
        sanitized = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')

        # Strip whitespace
        sanitized = sanitized.strip()

        if not sanitized:
            raise ValueError("Input cannot be empty after sanitization")

        return sanitized

# Global security manager instance
security_manager = SecurityManager()

# HTTP Bearer token scheme
security_scheme = HTTPBearer()

async def verify_api_key(credentials: HTTPAuthorizationCredentials = security_scheme) -> str:
    """Verify API key from Authorization header.

    Args:
        credentials: HTTP authorization credentials

    Returns:
        The validated API key

    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    api_key = credentials.credentials
    if not security_manager.validate_api_key(api_key):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return api_key

async def check_request_size(request: Request) -> None:
    """Check if request size is within limits.

    Args:
        request: The FastAPI request object

    Raises:
        HTTPException: If request is too large
    """
    content_length = request.headers.get("content-length")
    if content_length and int(content_length) > MAX_REQUEST_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"Request too large (max {MAX_REQUEST_SIZE} bytes)"
        )

def get_security_headers() -> Dict[str, str]:
    """Get security headers to add to responses.

    Returns:
        Dictionary of security headers
    """
    return {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }

class RateLimiter:
    """Simple in-memory rate limiter."""

    def __init__(self):
        """Initialize the rate limiter."""
        self.requests: Dict[str, list] = {}

    def is_allowed(self, identifier: str, limit: int = 100, window: int = 60) -> bool:
        """Check if a request is allowed based on rate limits.

        Args:
            identifier: Unique identifier (e.g., IP address)
            limit: Maximum number of requests allowed
            window: Time window in seconds

        Returns:
            True if request is allowed, False otherwise
        """
        current_time = time.time()

        if identifier not in self.requests:
            self.requests[identifier] = []

        # Clean old requests outside the window
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier]
            if current_time - req_time < window
        ]

        # Check if limit exceeded
        if len(self.requests[identifier]) >= limit:
            return False

        # Add current request
        self.requests[identifier].append(current_time)
        return True

# Global rate limiter instance
rate_limiter = RateLimiter()

def get_client_ip(request: Request) -> str:
    """Get client IP address from request.

    Args:
        request: The FastAPI request object

    Returns:
        Client IP address
    """
    # Check for forwarded headers first
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # Fall back to direct connection
    if hasattr(request.client, 'host'):
        return request.client.host

    return "unknown"