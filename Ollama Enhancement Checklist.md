# Ollama Enhancement Checklist

## Overview

This checklist guides a junior Python developer through integrating Ollama's full capabilities into the **existing** Neural Symbolic Language Model project. The project already has:

✅ **Local GPU-accelerated architecture** with PyTorch and FAISS-GPU
✅ **Symbolic reasoning engine** with multi-engine support (local/OpenAI/Anthropic)
✅ **Vector retrieval system** with hybrid FAISS/PyTorch fallback
✅ **OpenAI-compatible API** with FastAPI
✅ **Production-ready configuration** and monitoring

**Goal**: Enhance the existing `"local"` engine option to use **Ollama** instead of requiring manual model management, while preserving all existing functionality and improving the local AI capabilities.

## Prerequisites

### System Requirements
- [ ] **Ollama installed** - Download from [ollama.com](https://ollama.com/download)
- [ ] **Python 3.8+** with pip
- [ ] **8GB+ RAM** (16GB recommended)
- [ ] **GPU with 4GB+ VRAM** (optional but recommended)

### Verify Ollama Installation
```bash
# Check Ollama is running
ollama --version

# Pull a test model
ollama pull llama3.2

# Test basic functionality
ollama run llama3.2 "Hello, world!"
```

## Phase 1: Efficient Ollama Integration (Enhances Existing Architecture)

### Step 1.1: Install Ollama Python Library
- [ ] **Add dependency to existing requirements.txt**
  ```bash
  echo "ollama>=0.5.1" >> requirements.txt
  pip install ollama
  ```

### Step 1.2: Enhance Existing Symbolic Reasoning Engine
- [ ] **Modify `src/symbolic_reasoning.py` (EXISTING FILE)**
  - Add `ollama` as a new engine option alongside existing `local`, `openai`, `anthropic`
  - Integrate with existing GPU detection and configuration
  - Preserve all existing functionality

**Key Enhancement - Add to existing `SymbolicReasoner.__init__`:**
```python
# EXISTING: engine options are "local", "openai", "anthropic"
# ADD: "ollama" as new option that provides better local model management

if engine == "ollama":
    import ollama
    self.ollama_client = ollama.AsyncClient(host=host or "http://localhost:11434")
    self.model = model or "llama3.2"
    logger.info(f"SymbolicReasoner using Ollama with model: {self.model}")
```

### Step 1.3: Enhance Existing Configuration (Minimal Changes)
- [ ] **Add to existing `src/core/config.py` ModelSettings class**
  - Add Ollama settings to existing ModelSettings
  - Preserve all existing configuration structure

**Add to existing ModelSettings class:**
```python
# ADD these fields to existing ModelSettings class
ollama_host: str = Field(
    default="http://localhost:11434",
    description="Ollama server host URL"
)
ollama_timeout: PositiveInt = Field(
    default=300,
    description="Ollama request timeout in seconds"
)
```

### Step 1.4: Update Existing Environment Configuration
- [ ] **Add to existing `.env.example` (preserve existing structure)**

**Add these lines to existing .env.example:**
```bash
# ADD to existing Model Configuration section
MODEL_OLLAMA_HOST=http://localhost:11434
MODEL_OLLAMA_TIMEOUT=300

# MODIFY existing line to include ollama option:
# MODEL_REASONING_ENGINE=local  # local, openai, anthropic, ollama
```

## Phase 2: Enhanced Local Model Management (Builds on Existing System)

### Step 2.1: Implement Ollama Engine in Existing SymbolicReasoner
- [ ] **Enhance `src/symbolic_reasoning.py` process_query method**
  - Add Ollama processing path to existing method
  - Maintain existing interface and return types
  - Integrate with existing GPU detection and monitoring

**Add to existing `process_query` method:**
```python
# ADD this elif block to existing process_query method
elif self.engine == "ollama":
    return await self._process_ollama_query(query, context, stream)

# ADD new method to existing class
async def _process_ollama_query(self, query: str, context: Optional[str] = None, stream: bool = False):
    """Process query using Ollama - integrates with existing architecture."""
    messages = self._build_messages(query, context)  # Use existing helper

    try:
        if stream:
            return self.ollama_client.chat(model=self.model, messages=messages, stream=True)
        else:
            response = await self.ollama_client.chat(model=self.model, messages=messages)
            return response['message']['content']
    except Exception as e:
        logger.error(f"Ollama query failed: {e}")
        # Fallback to existing local engine if available
        if hasattr(self, '_process_local_query'):
            logger.info("Falling back to local engine")
            return await self._process_local_query(query, context)
        raise
```

### Step 2.2: Enhance Existing Retrieval System for Ollama Embeddings
- [ ] **Modify `src/retrieval.py` (EXISTING FILE)**
  - Add Ollama embedding support to existing embedding generation
  - Integrate with existing FAISS/PyTorch vector store architecture
  - Preserve existing caching and GPU optimization

**Add to existing `_generate_embeddings` method:**
```python
# ADD to existing embedding generation logic
if self.embedding_backend == "ollama":
    import ollama
    client = ollama.AsyncClient()
    embeddings = []
    for text in texts:
        response = await client.embed(model=self.embedding_model, input=text)
        embeddings.append(response['embeddings'][0])
    return np.array(embeddings)
```

## Phase 3: Streamlined API Enhancement (Leverages Existing FastAPI)

### Step 3.1: Enhance Existing OpenAI-Compatible Endpoints
- [ ] **Modify `src/main.py` existing chat endpoint**
  - The existing `/v1/chat/completions` endpoint already supports multiple engines
  - Simply ensure Ollama engine is properly routed through existing logic
  - Existing streaming support will work automatically

**Verify existing endpoint handles Ollama:**
```python
# EXISTING CODE in main.py already handles multiple engines
# Just ensure reasoner = SymbolicReasoner(engine=settings.model.reasoning_engine)
# will work with engine="ollama"

# NO MAJOR CHANGES NEEDED - existing architecture already supports this!
```

### Step 3.2: Add Ollama-Specific Management Endpoints (Optional Enhancement)
- [ ] **Add to existing `src/main.py`**
  - Add model management endpoints that complement existing system info
  - Integrate with existing monitoring and health check infrastructure

**Add these endpoints to existing FastAPI app:**
```python
@app.get("/ollama/models")
async def list_ollama_models():
    """List available Ollama models - complements existing /system/info"""
    if settings.model.reasoning_engine == "ollama":
        import ollama
        client = ollama.Client()
        models = client.list()
        return {"models": [m['name'] for m in models.get('models', [])]}
    return {"error": "Ollama engine not active"}

@app.post("/ollama/models/{model_name}/pull")
async def pull_ollama_model(model_name: str):
    """Pull Ollama model - enhances existing system management"""
    if settings.model.reasoning_engine == "ollama":
        import ollama
        client = ollama.Client()
        try:
            client.pull(model_name)
            return {"status": "success", "model": model_name}
        except Exception as e:
            return {"status": "error", "message": str(e)}
    return {"error": "Ollama engine not active"}
```

## Phase 4: Optimized Integration Testing (Validates Existing + New)

### Step 4.1: Test Ollama Integration with Existing System
- [ ] **Create `tests/test_ollama_integration.py`**
  - Test Ollama engine alongside existing engines
  - Verify existing functionality still works
  - Test fallback mechanisms

**Integration test example:**
```python
import pytest
from src.symbolic_reasoning import SymbolicReasoner

@pytest.mark.asyncio
async def test_ollama_engine_integration():
    """Test Ollama integrates with existing architecture."""
    # Test existing engines still work
    local_reasoner = SymbolicReasoner(engine="local")
    assert local_reasoner.engine == "local"

    # Test new Ollama engine
    ollama_reasoner = SymbolicReasoner(engine="ollama", model="llama3.2")
    assert ollama_reasoner.engine == "ollama"

    # Test both can process queries (if Ollama is available)
    try:
        response = await ollama_reasoner.process_query("Test query")
        assert isinstance(response, str)
        assert len(response) > 0
    except Exception as e:
        pytest.skip(f"Ollama not available: {e}")

@pytest.mark.asyncio
async def test_existing_api_with_ollama():
    """Test existing API endpoints work with Ollama engine."""
    from fastapi.testclient import TestClient
    from src.main import app

    client = TestClient(app)

    # Test existing endpoint with Ollama
    response = client.post("/v1/chat/completions", json={
        "model": "llama3.2",
        "messages": [{"role": "user", "content": "Hello"}]
    })

    # Should work regardless of engine (existing architecture handles this)
    assert response.status_code in [200, 503]  # 503 if Ollama not available
```

### Step 4.2: Validate Performance with Existing Monitoring
- [ ] **Verify existing monitoring captures Ollama metrics**
  - Existing `src/monitoring.py` should automatically track Ollama requests
  - Existing `/performance` endpoint should show Ollama stats
  - No additional monitoring code needed due to existing architecture

## Phase 5: Documentation and User Experience (Minimal Effort, High Value)

### Step 5.1: Update Existing Documentation
- [ ] **Update `docs/getting_started.rst`**
  - Add Ollama installation instructions
  - Update model configuration examples
  - Add troubleshooting for Ollama

**Add to existing getting started guide:**
```rst
Ollama Setup (Alternative to Cloud APIs)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

For fully local operation without API keys:

1. **Install Ollama**:

   Visit https://ollama.com/download and install for your OS

2. **Pull a model**:

   .. code-block:: bash

      ollama pull llama3.2
      ollama pull mxbai-embed-large

3. **Configure for Ollama**:

   .. code-block:: bash

      # In your .env file
      MODEL_REASONING_ENGINE=ollama
      MODEL_REASONING_MODEL=llama3.2
      MODEL_EMBEDDING_MODEL=mxbai-embed-large
```

### Step 5.2: Update Existing Configuration Documentation
- [ ] **Update `docs/configuration.rst`**
  - Document Ollama configuration options
  - Add performance tuning tips
  - Include model selection guidance

**Add to existing configuration guide:**
```rst
Ollama Engine Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~

The Ollama engine provides local LLM hosting with automatic model management:

.. code-block:: bash

   # Ollama Configuration
   MODEL_REASONING_ENGINE=ollama
   MODEL_OLLAMA_HOST=http://localhost:11434
   MODEL_OLLAMA_TIMEOUT=300

   # Model Selection
   MODEL_REASONING_MODEL=llama3.2      # For general chat
   MODEL_EMBEDDING_MODEL=mxbai-embed-large  # For embeddings
```

## Phase 6: Production Readiness (Leverages Existing Infrastructure)

### Step 6.1: Enhance Existing Docker Support
- [ ] **Update existing deployment documentation**
  - Add Ollama service to existing Docker Compose
  - Update existing Kubernetes manifests
  - Leverage existing monitoring and logging

**Add to existing docker-compose.yml:**
```yaml
services:
  # Existing neural-symbolic-api service
  neural-symbolic-api:
    # ... existing configuration
    depends_on:
      - ollama  # Add dependency
    environment:
      - MODEL_REASONING_ENGINE=ollama
      - MODEL_OLLAMA_HOST=http://ollama:11434

  # Add Ollama service
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    # GPU support (if available)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

volumes:
  ollama_data:
```

### Step 6.2: Update Existing Health Checks
- [ ] **Enhance existing health check endpoint**
  - Add Ollama connectivity check to existing `/health` endpoint
  - Integrate with existing monitoring infrastructure

**Add to existing health check in `src/main.py`:**
```python
@app.get("/health")
async def health_check():
    """Enhanced health check including Ollama."""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "components": {
            "api": "healthy",
            "gpu": "available" if torch.cuda.is_available() else "unavailable"
        }
    }

    # Add Ollama health check if using Ollama engine
    if settings.model.reasoning_engine == "ollama":
        try:
            import ollama
            client = ollama.Client(host=settings.model.ollama_host)
            models = client.list()
            health_status["components"]["ollama"] = "healthy"
            health_status["components"]["ollama_models"] = len(models.get('models', []))
        except Exception as e:
            health_status["components"]["ollama"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

    return health_status
```

## Efficient Implementation Summary

### Why This Approach is Optimal

**✅ Minimal Code Changes**: Only 3-4 files need modification
- `src/symbolic_reasoning.py` - Add Ollama engine option
- `src/core/config.py` - Add Ollama settings
- `src/retrieval.py` - Add Ollama embeddings
- `.env.example` - Add configuration options

**✅ Preserves Existing Architecture**:
- All existing engines (local, OpenAI, Anthropic) continue to work
- Existing GPU acceleration and FAISS integration preserved
- Existing API endpoints and monitoring unchanged
- Existing Docker and deployment configurations work

**✅ Leverages Existing Infrastructure**:
- FastAPI routing automatically handles new engine
- Existing monitoring captures Ollama metrics
- Existing health checks extended for Ollama
- Existing error handling and fallbacks work

**✅ Immediate Value**:
- Better local model management (auto-pull, model switching)
- Improved privacy (no API keys needed)
- Cost reduction (no per-token charges)
- Enhanced reliability (no internet dependency)

### Implementation Time Estimate

**Phase 1-2**: 2-3 hours (Core integration)
**Phase 3-4**: 1-2 hours (API and testing)
**Phase 5-6**: 1-2 hours (Documentation and deployment)

**Total**: 4-7 hours for full integration

### Validation Checklist (Streamlined)

### Essential Testing
- [ ] **Existing functionality preserved** (all engines work)
- [ ] **Ollama engine works** (basic chat and embeddings)
- [ ] **Configuration works** (environment variables)
- [ ] **Health checks include Ollama** (monitoring integration)
- [ ] **Documentation updated** (getting started guide)

### Success Criteria
- [ ] **Drop-in replacement**: Change `MODEL_REASONING_ENGINE=ollama` and it works
- [ ] **No breaking changes**: Existing deployments unaffected
- [ ] **Enhanced capabilities**: Better local model management
- [ ] **Production ready**: Docker, health checks, monitoring all work

## Troubleshooting Guide

### Common Issues
1. **Ollama not running**: Check `ollama serve` is active
2. **Model not found**: Run `ollama pull <model-name>`
3. **Connection timeout**: Increase `OLLAMA_TIMEOUT` setting
4. **Memory issues**: Use smaller models or increase system RAM
5. **GPU not detected**: Check CUDA installation and GPU drivers

### Debug Commands
```bash
# Check Ollama status
ollama ps

# Test model directly
ollama run llama3.2 "Test message"

# Check logs
ollama logs

# Monitor resource usage
nvidia-smi  # For GPU
htop        # For CPU/RAM
```

## Success Criteria

✅ **Phase 1-3 Complete**: Basic Ollama integration working
✅ **Phase 4-6 Complete**: Advanced features implemented
✅ **Phase 7-9 Complete**: Production-ready with full feature set
✅ **Phase 10-11 Complete**: Fully tested and documented

## Next Steps After Completion

1. **Performance optimization** based on usage patterns
2. **Custom model integration** for specific use cases
3. **Advanced RAG techniques** with Ollama embeddings
4. **Multi-modal capabilities** with vision models
5. **Edge deployment** optimization

---

## Detailed Implementation Guide

### Phase 1 Implementation Details

#### Step 1.2: Complete OllamaClient Implementation

**File: `src/ollama_client.py`**
```python
import ollama
import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from src.core.config import get_settings

logger = logging.getLogger(__name__)

class OllamaClient:
    """Enhanced Ollama client with full feature support."""

    def __init__(self, host: str = "http://localhost:11434"):
        self.host = host
        self.client = ollama.Client(host=host)
        self.async_client = ollama.AsyncClient(host=host)
        self.settings = get_settings()

    async def health_check(self) -> bool:
        """Check if Ollama server is healthy."""
        try:
            models = await self.async_client.list()
            return True
        except Exception as e:
            logger.error(f"Ollama health check failed: {e}")
            return False

    async def chat(
        self,
        model: str,
        messages: List[Dict[str, str]],
        stream: bool = False,
        **kwargs
    ) -> Union[Dict, AsyncGenerator]:
        """Enhanced chat with streaming support."""
        try:
            if stream:
                return self.async_client.chat(
                    model=model,
                    messages=messages,
                    stream=True,
                    **kwargs
                )
            else:
                response = await self.async_client.chat(
                    model=model,
                    messages=messages,
                    stream=False,
                    **kwargs
                )
                return response
        except ollama.ResponseError as e:
            if e.status_code == 404:
                logger.info(f"Model {model} not found, attempting to pull...")
                await self.pull_model(model)
                return await self.chat(model, messages, stream, **kwargs)
            raise

    async def generate_embeddings(
        self,
        model: str,
        input_text: Union[str, List[str]]
    ) -> List[List[float]]:
        """Generate embeddings with batch support."""
        try:
            if isinstance(input_text, str):
                input_text = [input_text]

            embeddings = []
            for text in input_text:
                response = await self.async_client.embed(
                    model=model,
                    input=text
                )
                embeddings.append(response['embeddings'][0])

            return embeddings
        except ollama.ResponseError as e:
            if e.status_code == 404:
                logger.info(f"Embedding model {model} not found, attempting to pull...")
                await self.pull_model(model)
                return await self.generate_embeddings(model, input_text)
            raise

    async def list_models(self) -> List[Dict[str, Any]]:
        """List all available models."""
        response = await self.async_client.list()
        return response.get('models', [])

    async def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry."""
        try:
            logger.info(f"Pulling model: {model_name}")
            await self.async_client.pull(model_name)
            logger.info(f"Successfully pulled model: {model_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
            return False

    async def delete_model(self, model_name: str) -> bool:
        """Delete a model."""
        try:
            await self.async_client.delete(model_name)
            logger.info(f"Deleted model: {model_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete model {model_name}: {e}")
            return False

    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get detailed information about a model."""
        try:
            response = await self.async_client.show(model_name)
            return response
        except Exception as e:
            logger.error(f"Failed to get info for model {model_name}: {e}")
            return {}
```

#### Step 1.3: Configuration Updates

**Add to `src/core/config.py`:**
```python
class OllamaSettings(BaseSettings):
    """Ollama-specific configuration settings."""

    class Config:
        env_prefix = "OLLAMA_"
        case_sensitive = False

    host: str = Field(
        default="http://localhost:11434",
        description="Ollama server host URL"
    )
    default_model: str = Field(
        default="llama3.2",
        description="Default chat model"
    )
    embedding_model: str = Field(
        default="mxbai-embed-large",
        description="Default embedding model"
    )
    timeout: PositiveInt = Field(
        default=300,
        description="Request timeout in seconds"
    )
    keep_alive: str = Field(
        default="5m",
        description="How long to keep models loaded"
    )
    auto_pull: bool = Field(
        default=True,
        description="Automatically pull missing models"
    )
    max_concurrent_requests: PositiveInt = Field(
        default=10,
        description="Maximum concurrent requests to Ollama"
    )
```

### Phase 2 Implementation Details

#### Step 2.1: Enhanced Symbolic Reasoning

**Update `src/symbolic_reasoning.py`:**
```python
from src.ollama_client import OllamaClient

class SymbolicReasoner:
    def __init__(self, engine: str = "local", model: str = "llama", use_gpu: bool = True):
        # ... existing code ...

        # Add Ollama support
        if engine == "ollama":
            self.ollama_client = OllamaClient()
            self.model = model or "llama3.2"

    async def _process_ollama_query(
        self,
        query: str,
        context: Optional[str] = None,
        stream: bool = False
    ) -> Union[str, AsyncGenerator]:
        """Process query using Ollama."""
        messages = []

        # Add system message for symbolic reasoning
        system_prompt = """You are an advanced AI assistant specializing in symbolic reasoning and logical analysis.
        Provide clear, step-by-step reasoning for complex problems. When possible, show your logical steps explicitly."""
        messages.append({"role": "system", "content": system_prompt})

        # Add context if provided
        if context:
            messages.append({
                "role": "system",
                "content": f"Additional context: {context}"
            })

        # Add user query
        messages.append({"role": "user", "content": query})

        # Process with Ollama
        response = await self.ollama_client.chat(
            model=self.model,
            messages=messages,
            stream=stream,
            temperature=0.7,
            top_p=0.9
        )

        if stream:
            return response
        else:
            return response['message']['content']

    async def process_query(
        self,
        query: str,
        context: Optional[str] = None,
        stream: bool = False
    ) -> Union[str, AsyncGenerator]:
        """Enhanced process_query with Ollama support."""
        if self.engine == "ollama":
            return await self._process_ollama_query(query, context, stream)
        else:
            # Existing implementation for other engines
            return self._process_local_query(query, context)
```

### Phase 4 Implementation Details

#### Step 4.1: Streaming Implementation

**Add to `src/main.py`:**
```python
from fastapi.responses import StreamingResponse
import json

@app.post("/v1/chat/completions")
async def enhanced_chat_completions(request: OpenAIChatRequest):
    """Enhanced OpenAI-compatible chat endpoint with Ollama support."""

    # Check if using Ollama engine
    settings = get_settings()
    if settings.model.reasoning_engine == "ollama":
        ollama_client = OllamaClient()

        if request.stream:
            # Return streaming response
            async def generate_stream():
                async for chunk in await ollama_client.chat(
                    model=request.model,
                    messages=[msg.dict() for msg in request.messages],
                    stream=True,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens
                ):
                    # Format as OpenAI-compatible streaming response
                    openai_chunk = {
                        "id": f"chatcmpl-{uuid.uuid4()}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": request.model,
                        "choices": [{
                            "index": 0,
                            "delta": {
                                "content": chunk.get('message', {}).get('content', '')
                            },
                            "finish_reason": None
                        }]
                    }
                    yield f"data: {json.dumps(openai_chunk)}\n\n"

                # Send final chunk
                final_chunk = {
                    "id": f"chatcmpl-{uuid.uuid4()}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": request.model,
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": "stop"
                    }]
                }
                yield f"data: {json.dumps(final_chunk)}\n\n"
                yield "data: [DONE]\n\n"

            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache"}
            )
        else:
            # Non-streaming response
            response = await ollama_client.chat(
                model=request.model,
                messages=[msg.dict() for msg in request.messages],
                stream=False,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )

            return OpenAIChatResponse(
                id=f"chatcmpl-{uuid.uuid4()}",
                object="chat.completion",
                created=int(time.time()),
                model=request.model,
                choices=[
                    OpenAIChoice(
                        index=0,
                        message=OpenAIMessage(
                            role="assistant",
                            content=response['message']['content']
                        ),
                        finish_reason="stop"
                    )
                ],
                usage=OpenAIUsage(
                    prompt_tokens=response.get('prompt_eval_count', 0),
                    completion_tokens=response.get('eval_count', 0),
                    total_tokens=response.get('prompt_eval_count', 0) + response.get('eval_count', 0)
                )
            )

    # Fallback to existing implementation
    # ... existing code ...

@app.post("/v1/embeddings")
async def generate_embeddings(request: EmbeddingRequest):
    """Generate embeddings using Ollama."""
    settings = get_settings()
    ollama_client = OllamaClient()

    # Generate embeddings
    embeddings = await ollama_client.generate_embeddings(
        model=settings.ollama.embedding_model,
        input_text=request.input
    )

    # Format as OpenAI-compatible response
    embedding_objects = []
    for i, embedding in enumerate(embeddings):
        embedding_objects.append({
            "object": "embedding",
            "embedding": embedding,
            "index": i
        })

    return {
        "object": "list",
        "data": embedding_objects,
        "model": settings.ollama.embedding_model,
        "usage": {
            "prompt_tokens": len(str(request.input)),
            "total_tokens": len(str(request.input))
        }
    }

@app.get("/v1/models")
async def list_models():
    """List available Ollama models."""
    ollama_client = OllamaClient()
    models = await ollama_client.list_models()

    # Format as OpenAI-compatible response
    model_objects = []
    for model in models:
        model_objects.append({
            "id": model.get('name', ''),
            "object": "model",
            "created": int(time.time()),
            "owned_by": "ollama"
        })

    return {
        "object": "list",
        "data": model_objects
    }
```

### Testing Implementation

**Create `tests/test_ollama_integration.py`:**
```python
import pytest
import asyncio
from src.ollama_client import OllamaClient
from src.symbolic_reasoning import SymbolicReasoner

@pytest.mark.asyncio
async def test_ollama_client_health():
    """Test Ollama client health check."""
    client = OllamaClient()
    health = await client.health_check()
    assert isinstance(health, bool)

@pytest.mark.asyncio
async def test_ollama_chat():
    """Test basic chat functionality."""
    client = OllamaClient()

    messages = [
        {"role": "user", "content": "What is 2+2?"}
    ]

    response = await client.chat(
        model="llama3.2",
        messages=messages
    )

    assert 'message' in response
    assert 'content' in response['message']
    assert len(response['message']['content']) > 0

@pytest.mark.asyncio
async def test_ollama_embeddings():
    """Test embedding generation."""
    client = OllamaClient()

    embeddings = await client.generate_embeddings(
        model="mxbai-embed-large",
        input_text="Hello world"
    )

    assert len(embeddings) == 1
    assert len(embeddings[0]) > 0
    assert all(isinstance(x, float) for x in embeddings[0])

@pytest.mark.asyncio
async def test_symbolic_reasoning_ollama():
    """Test symbolic reasoning with Ollama."""
    reasoner = SymbolicReasoner(engine="ollama", model="llama3.2")

    response = await reasoner.process_query(
        "If A implies B, and B implies C, what can we conclude about A and C?"
    )

    assert isinstance(response, str)
    assert len(response) > 0
```

---

## Quick Start Implementation Example

### Minimal Working Example (30 minutes)

For junior developers who want to see Ollama working quickly, here's a minimal implementation:

**1. Install Ollama and pull a model:**
```bash
# Install Ollama (visit ollama.com for your OS)
ollama pull llama3.2
ollama pull mxbai-embed-large
```

**2. Add to requirements.txt:**
```
ollama>=0.5.1
```

**3. Create simple test script `test_ollama.py`:**
```python
import ollama
import asyncio

async def test_ollama():
    client = ollama.AsyncClient()

    # Test chat
    response = await client.chat(
        model='llama3.2',
        messages=[{'role': 'user', 'content': 'Hello!'}]
    )
    print("Chat:", response['message']['content'])

    # Test embeddings
    embedding = await client.embed(
        model='mxbai-embed-large',
        input='Hello world'
    )
    print("Embedding length:", len(embedding['embeddings'][0]))

if __name__ == "__main__":
    asyncio.run(test_ollama())
```

**4. Run test:**
```bash
python test_ollama.py
```

If this works, you're ready to proceed with the full integration!

## Common Pitfalls and Solutions

### Issue 1: "Model not found" errors
**Solution**: Always check if model exists before using:
```python
try:
    response = await client.chat(model='llama3.2', messages=messages)
except ollama.ResponseError as e:
    if e.status_code == 404:
        print("Pulling model...")
        await client.pull('llama3.2')
        response = await client.chat(model='llama3.2', messages=messages)
```

### Issue 2: Slow responses
**Solutions**:
- Use smaller models for development (`llama3.2:1b`)
- Implement response caching
- Use streaming for better UX

### Issue 3: Memory issues
**Solutions**:
- Set `keep_alive=0` to unload models immediately
- Use model rotation for multiple models
- Monitor memory usage

### Issue 4: Integration with existing code
**Solution**: Create adapter pattern:
```python
class LLMAdapter:
    def __init__(self, engine="ollama"):
        if engine == "ollama":
            self.client = ollama.AsyncClient()
        # ... other engines

    async def generate(self, prompt, **kwargs):
        if self.engine == "ollama":
            return await self._ollama_generate(prompt, **kwargs)
        # ... other implementations
```

## Step-by-Step Debugging Guide

### Debug Step 1: Verify Ollama Installation
```bash
ollama --version
ollama list
ollama ps
```

### Debug Step 2: Test Basic Connectivity
```python
import ollama
try:
    client = ollama.Client()
    models = client.list()
    print("Connected! Available models:", [m['name'] for m in models['models']])
except Exception as e:
    print("Connection failed:", e)
```

### Debug Step 3: Test Model Loading
```python
import ollama
client = ollama.Client()
try:
    response = client.generate(model='llama3.2', prompt='Test')
    print("Model working!")
except Exception as e:
    print("Model issue:", e)
    # Try pulling the model
    client.pull('llama3.2')
```

### Debug Step 4: Test Integration Points
```python
# Test each integration point separately
from src.ollama_client import OllamaClient
from src.symbolic_reasoning import SymbolicReasoner

# Test client
client = OllamaClient()
health = await client.health_check()
print("Client health:", health)

# Test reasoner
reasoner = SymbolicReasoner(engine="ollama")
response = await reasoner.process_query("Test query")
print("Reasoner working:", bool(response))
```

## Performance Optimization Tips

### Tip 1: Model Selection
```python
# Development: Use small, fast models
DEVELOPMENT_MODELS = {
    "chat": "llama3.2:1b",
    "embedding": "nomic-embed-text"
}

# Production: Use larger, more capable models
PRODUCTION_MODELS = {
    "chat": "llama3.2:8b",
    "embedding": "mxbai-embed-large"
}
```

### Tip 2: Connection Pooling
```python
class OllamaPool:
    def __init__(self, max_connections=10):
        self.clients = [ollama.AsyncClient() for _ in range(max_connections)]
        self.available = asyncio.Queue()
        for client in self.clients:
            self.available.put_nowait(client)

    async def get_client(self):
        return await self.available.get()

    def return_client(self, client):
        self.available.put_nowait(client)
```

### Tip 3: Response Caching
```python
import hashlib
from functools import lru_cache

class CachedOllamaClient:
    def __init__(self):
        self.client = ollama.AsyncClient()
        self.cache = {}

    def _cache_key(self, model, messages):
        content = f"{model}:{str(messages)}"
        return hashlib.md5(content.encode()).hexdigest()

    async def chat(self, model, messages, **kwargs):
        if not kwargs.get('stream', False):  # Only cache non-streaming
            key = self._cache_key(model, messages)
            if key in self.cache:
                return self.cache[key]

        response = await self.client.chat(model=model, messages=messages, **kwargs)

        if not kwargs.get('stream', False):
            self.cache[key] = response

        return response
```

## Production Deployment Checklist

### Infrastructure Requirements
- [ ] **Ollama server with adequate resources**
  - 16GB+ RAM for 7B models
  - 32GB+ RAM for 13B models
  - GPU with 8GB+ VRAM (recommended)

- [ ] **Network configuration**
  - Ollama accessible from application server
  - Proper firewall rules
  - Load balancer configuration (if multiple Ollama instances)

### Configuration Management
- [ ] **Environment-specific settings**
```python
# config/production.yaml
ollama:
  host: "http://ollama-server:11434"
  default_model: "llama3.2:8b"
  embedding_model: "mxbai-embed-large"
  timeout: 300
  max_concurrent_requests: 20

# config/development.yaml
ollama:
  host: "http://localhost:11434"
  default_model: "llama3.2:1b"
  embedding_model: "nomic-embed-text"
  timeout: 60
  max_concurrent_requests: 5
```

### Monitoring Setup
- [ ] **Health check endpoints**
- [ ] **Performance metrics collection**
- [ ] **Error rate monitoring**
- [ ] **Resource usage alerts**

### Security Considerations
- [ ] **Network security** (VPN, firewall rules)
- [ ] **Input validation** and sanitization
- [ ] **Rate limiting** per user/API key
- [ ] **Audit logging** for all requests

**Note**: This checklist should be completed incrementally, testing each phase before moving to the next. Each step builds upon the previous ones, so maintain working functionality throughout the process.
