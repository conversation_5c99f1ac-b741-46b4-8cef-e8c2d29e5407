# Ollama Enhancement Checklist

## Overview

This checklist guides a junior Python developer through integrating Ollama's full capabilities into the Neural Symbolic Language Model project. Ollama provides local LLM hosting with OpenAI-compatible APIs, streaming, embeddings, and advanced features.

## Prerequisites

### System Requirements
- [ ] **Ollama installed** - Download from [ollama.com](https://ollama.com/download)
- [ ] **Python 3.8+** with pip
- [ ] **8GB+ RAM** (16GB recommended)
- [ ] **GPU with 4GB+ VRAM** (optional but recommended)

### Verify Ollama Installation
```bash
# Check Ollama is running
ollama --version

# Pull a test model
ollama pull llama3.2

# Test basic functionality
ollama run llama3.2 "Hello, world!"
```

## Phase 1: Core Ollama Integration

### Step 1.1: Install Ollama Python Library
- [ ] **Add dependency to requirements.txt**
  ```bash
  echo "ollama>=0.5.1" >> requirements.txt
  pip install ollama
  ```

### Step 1.2: Create Ollama Client Module
- [ ] **Create `src/ollama_client.py`**
  - Import ollama library
  - Create OllamaClient class with connection management
  - Add error handling and retry logic
  - Implement health checks

**File to create:** `src/ollama_client.py`
```python
import ollama
from typing import List, Dict, Any, Optional, AsyncGenerator
import logging
from src.core.config import get_settings

class OllamaClient:
    def __init__(self, host: str = "http://localhost:11434"):
        self.client = ollama.Client(host=host)
        self.async_client = ollama.AsyncClient(host=host)
        
    async def chat(self, model: str, messages: List[Dict], **kwargs):
        # Implementation here
        pass
        
    async def generate_embeddings(self, model: str, text: str):
        # Implementation here
        pass
```

### Step 1.3: Update Configuration
- [ ] **Modify `src/core/config.py`**
  - Add Ollama-specific settings
  - Add model management configuration
  - Add embedding model settings

**Settings to add:**
```python
# Ollama Configuration
OLLAMA_HOST: str = "http://localhost:11434"
OLLAMA_DEFAULT_MODEL: str = "llama3.2"
OLLAMA_EMBEDDING_MODEL: str = "mxbai-embed-large"
OLLAMA_TIMEOUT: int = 300
OLLAMA_KEEP_ALIVE: str = "5m"
```

### Step 1.4: Update Environment Configuration
- [ ] **Modify `.env.example`**
  - Add Ollama configuration variables
  - Update model engine options

**Environment variables to add:**
```bash
# Ollama Configuration
MODEL_REASONING_ENGINE=ollama
OLLAMA_HOST=http://localhost:11434
OLLAMA_DEFAULT_MODEL=llama3.2
OLLAMA_EMBEDDING_MODEL=mxbai-embed-large
OLLAMA_TIMEOUT=300
OLLAMA_KEEP_ALIVE=5m
```

## Phase 2: Enhanced Symbolic Reasoning with Ollama

### Step 2.1: Modify Symbolic Reasoning Engine
- [ ] **Update `src/symbolic_reasoning.py`**
  - Add Ollama engine support
  - Implement streaming responses
  - Add model switching capabilities
  - Integrate with OllamaClient

**Key modifications:**
1. Add `ollama` to engine options in `__init__`
2. Create `_process_ollama_query` method
3. Add streaming support with `stream=True`
4. Implement model validation and auto-pulling

### Step 2.2: Add Model Management
- [ ] **Create `src/ollama_models.py`**
  - List available models
  - Pull/delete models automatically
  - Model health checking
  - Performance monitoring per model

**Features to implement:**
```python
class OllamaModelManager:
    async def list_models(self) -> List[str]
    async def pull_model(self, model_name: str) -> bool
    async def delete_model(self, model_name: str) -> bool
    async def get_model_info(self, model_name: str) -> Dict
```

## Phase 3: Advanced Embedding Integration

### Step 3.1: Enhance Vector Retrieval with Ollama Embeddings
- [ ] **Update `src/retrieval.py`**
  - Add Ollama embedding support
  - Implement batch embedding generation
  - Add embedding model switching
  - Optimize for local processing

**Key changes:**
1. Add `ollama` embedding backend option
2. Implement `_generate_ollama_embeddings` method
3. Add batch processing for multiple texts
4. Cache embeddings locally

### Step 3.2: Update Hybrid Retriever
- [ ] **Modify `src/hybrid_retriever.py`**
  - Support Ollama embedding models
  - Add embedding model configuration
  - Implement embedding caching

## Phase 4: Streaming and Real-time Features

### Step 4.1: Implement Streaming Chat
- [ ] **Update `src/main.py`**
  - Add Ollama streaming endpoint
  - Implement Server-Sent Events (SSE)
  - Add WebSocket support for real-time chat

**New endpoints to add:**
```python
@app.post("/v1/chat/completions/stream")
async def ollama_chat_stream(request: ChatRequest):
    # Streaming implementation
    pass

@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    # WebSocket implementation
    pass
```

### Step 4.2: Add Progress Tracking
- [ ] **Create `src/streaming_utils.py`**
  - Progress indicators for long responses
  - Token counting and rate limiting
  - Response quality metrics

## Phase 5: Multi-Model Support

### Step 5.1: Model Routing and Load Balancing
- [ ] **Create `src/model_router.py`**
  - Route requests to appropriate models
  - Load balance across multiple models
  - Fallback mechanisms

**Features:**
```python
class ModelRouter:
    def route_request(self, request_type: str, complexity: str) -> str
    def get_best_model(self, task_type: str) -> str
    def health_check_models(self) -> Dict[str, bool]
```

### Step 5.2: Specialized Model Integration
- [ ] **Add support for specialized models:**
  - **Code models**: `codellama`, `deepseek-coder`
  - **Math models**: `mathstral`
  - **Vision models**: `llava`, `llama3.2-vision`
  - **Embedding models**: `mxbai-embed-large`, `nomic-embed-text`

## Phase 6: Performance Optimization

### Step 6.1: Caching and Memory Management
- [ ] **Create `src/ollama_cache.py`**
  - Response caching for identical queries
  - Model context caching
  - Memory-efficient model loading

### Step 6.2: Batch Processing
- [ ] **Implement batch operations:**
  - Batch embedding generation
  - Batch inference for multiple queries
  - Queue management for high load

### Step 6.3: GPU Optimization
- [ ] **Add GPU utilization features:**
  - GPU memory monitoring
  - Model quantization support
  - Multi-GPU load balancing

## Phase 7: Advanced Features

### Step 7.1: Function Calling Support
- [ ] **Create `src/function_calling.py`**
  - Define function schemas
  - Implement function execution
  - Add safety and validation

**Example implementation:**
```python
class FunctionCaller:
    def register_function(self, name: str, func: callable, schema: Dict)
    async def execute_function(self, name: str, args: Dict) -> Any
    def validate_function_call(self, call: Dict) -> bool
```

### Step 7.2: Vision Support (if applicable)
- [ ] **Add vision capabilities:**
  - Image processing endpoints
  - Multi-modal chat support
  - Image embedding generation

### Step 7.3: Custom Model Support
- [ ] **Add custom model features:**
  - Model fine-tuning integration
  - Custom prompt templates
  - Model adaptation for specific tasks

## Phase 8: Monitoring and Observability

### Step 8.1: Ollama-Specific Metrics
- [ ] **Update `src/monitoring.py`**
  - Model performance metrics
  - Token usage tracking
  - Response quality scoring
  - Ollama server health monitoring

**Metrics to track:**
```python
- ollama_requests_total
- ollama_response_time_seconds
- ollama_tokens_generated_total
- ollama_model_memory_usage_bytes
- ollama_active_models_count
```

### Step 8.2: Health Checks and Diagnostics
- [ ] **Create `src/ollama_health.py`**
  - Ollama server connectivity
  - Model availability checks
  - Performance benchmarking
  - Resource usage monitoring

## Phase 9: API Enhancements

### Step 9.1: OpenAI Compatibility Layer
- [ ] **Enhance OpenAI compatibility:**
  - Full parameter support (temperature, top_p, etc.)
  - Proper error handling and status codes
  - Response format standardization

### Step 9.2: Additional Endpoints
- [ ] **Add new API endpoints:**
  ```python
  GET /v1/models - List available models
  POST /v1/embeddings - Generate embeddings
  GET /v1/models/{model}/info - Get model information
  POST /v1/models/{model}/pull - Pull a model
  DELETE /v1/models/{model} - Delete a model
  ```

## Phase 10: Testing and Documentation

### Step 10.1: Comprehensive Testing
- [ ] **Create test files:**
  - `tests/test_ollama_client.py`
  - `tests/test_ollama_integration.py`
  - `tests/test_streaming.py`
  - `tests/test_embeddings.py`

### Step 10.2: Update Documentation
- [ ] **Update documentation files:**
  - `docs/ollama_integration.rst`
  - `docs/getting_started.rst` (add Ollama setup)
  - `docs/api_reference.rst` (add new endpoints)
  - `README.md` (update installation instructions)

### Step 10.3: Example Scripts
- [ ] **Create example files:**
  - `examples/ollama_basic_chat.py`
  - `examples/ollama_streaming_chat.py`
  - `examples/ollama_embeddings.py`
  - `examples/ollama_function_calling.py`

## Phase 11: Production Readiness

### Step 11.1: Configuration Management
- [ ] **Add production configurations:**
  - Docker support for Ollama
  - Environment-specific model configurations
  - Resource limits and quotas

### Step 11.2: Error Handling and Resilience
- [ ] **Implement robust error handling:**
  - Connection retry logic
  - Graceful degradation
  - Circuit breaker patterns
  - Fallback to cloud APIs

### Step 11.3: Security Enhancements
- [ ] **Add security features:**
  - Model access controls
  - Request validation and sanitization
  - Rate limiting per model
  - Audit logging

## Validation Checklist

### Functional Testing
- [ ] **Basic chat functionality works**
- [ ] **Streaming responses work correctly**
- [ ] **Embeddings generation works**
- [ ] **Model switching works**
- [ ] **Error handling works properly**

### Performance Testing
- [ ] **Response times are acceptable (<2s for simple queries)**
- [ ] **Memory usage is reasonable**
- [ ] **Concurrent requests handled properly**
- [ ] **GPU utilization is optimal (if available)**

### Integration Testing
- [ ] **OpenAI compatibility maintained**
- [ ] **All existing functionality still works**
- [ ] **New endpoints return correct responses**
- [ ] **Documentation is accurate and complete**

## Troubleshooting Guide

### Common Issues
1. **Ollama not running**: Check `ollama serve` is active
2. **Model not found**: Run `ollama pull <model-name>`
3. **Connection timeout**: Increase `OLLAMA_TIMEOUT` setting
4. **Memory issues**: Use smaller models or increase system RAM
5. **GPU not detected**: Check CUDA installation and GPU drivers

### Debug Commands
```bash
# Check Ollama status
ollama ps

# Test model directly
ollama run llama3.2 "Test message"

# Check logs
ollama logs

# Monitor resource usage
nvidia-smi  # For GPU
htop        # For CPU/RAM
```

## Success Criteria

✅ **Phase 1-3 Complete**: Basic Ollama integration working
✅ **Phase 4-6 Complete**: Advanced features implemented
✅ **Phase 7-9 Complete**: Production-ready with full feature set
✅ **Phase 10-11 Complete**: Fully tested and documented

## Next Steps After Completion

1. **Performance optimization** based on usage patterns
2. **Custom model integration** for specific use cases
3. **Advanced RAG techniques** with Ollama embeddings
4. **Multi-modal capabilities** with vision models
5. **Edge deployment** optimization

---

## Detailed Implementation Guide

### Phase 1 Implementation Details

#### Step 1.2: Complete OllamaClient Implementation

**File: `src/ollama_client.py`**
```python
import ollama
import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from src.core.config import get_settings

logger = logging.getLogger(__name__)

class OllamaClient:
    """Enhanced Ollama client with full feature support."""

    def __init__(self, host: str = "http://localhost:11434"):
        self.host = host
        self.client = ollama.Client(host=host)
        self.async_client = ollama.AsyncClient(host=host)
        self.settings = get_settings()

    async def health_check(self) -> bool:
        """Check if Ollama server is healthy."""
        try:
            models = await self.async_client.list()
            return True
        except Exception as e:
            logger.error(f"Ollama health check failed: {e}")
            return False

    async def chat(
        self,
        model: str,
        messages: List[Dict[str, str]],
        stream: bool = False,
        **kwargs
    ) -> Union[Dict, AsyncGenerator]:
        """Enhanced chat with streaming support."""
        try:
            if stream:
                return self.async_client.chat(
                    model=model,
                    messages=messages,
                    stream=True,
                    **kwargs
                )
            else:
                response = await self.async_client.chat(
                    model=model,
                    messages=messages,
                    stream=False,
                    **kwargs
                )
                return response
        except ollama.ResponseError as e:
            if e.status_code == 404:
                logger.info(f"Model {model} not found, attempting to pull...")
                await self.pull_model(model)
                return await self.chat(model, messages, stream, **kwargs)
            raise

    async def generate_embeddings(
        self,
        model: str,
        input_text: Union[str, List[str]]
    ) -> List[List[float]]:
        """Generate embeddings with batch support."""
        try:
            if isinstance(input_text, str):
                input_text = [input_text]

            embeddings = []
            for text in input_text:
                response = await self.async_client.embed(
                    model=model,
                    input=text
                )
                embeddings.append(response['embeddings'][0])

            return embeddings
        except ollama.ResponseError as e:
            if e.status_code == 404:
                logger.info(f"Embedding model {model} not found, attempting to pull...")
                await self.pull_model(model)
                return await self.generate_embeddings(model, input_text)
            raise

    async def list_models(self) -> List[Dict[str, Any]]:
        """List all available models."""
        response = await self.async_client.list()
        return response.get('models', [])

    async def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry."""
        try:
            logger.info(f"Pulling model: {model_name}")
            await self.async_client.pull(model_name)
            logger.info(f"Successfully pulled model: {model_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
            return False

    async def delete_model(self, model_name: str) -> bool:
        """Delete a model."""
        try:
            await self.async_client.delete(model_name)
            logger.info(f"Deleted model: {model_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete model {model_name}: {e}")
            return False

    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get detailed information about a model."""
        try:
            response = await self.async_client.show(model_name)
            return response
        except Exception as e:
            logger.error(f"Failed to get info for model {model_name}: {e}")
            return {}
```

#### Step 1.3: Configuration Updates

**Add to `src/core/config.py`:**
```python
class OllamaSettings(BaseSettings):
    """Ollama-specific configuration settings."""

    class Config:
        env_prefix = "OLLAMA_"
        case_sensitive = False

    host: str = Field(
        default="http://localhost:11434",
        description="Ollama server host URL"
    )
    default_model: str = Field(
        default="llama3.2",
        description="Default chat model"
    )
    embedding_model: str = Field(
        default="mxbai-embed-large",
        description="Default embedding model"
    )
    timeout: PositiveInt = Field(
        default=300,
        description="Request timeout in seconds"
    )
    keep_alive: str = Field(
        default="5m",
        description="How long to keep models loaded"
    )
    auto_pull: bool = Field(
        default=True,
        description="Automatically pull missing models"
    )
    max_concurrent_requests: PositiveInt = Field(
        default=10,
        description="Maximum concurrent requests to Ollama"
    )
```

### Phase 2 Implementation Details

#### Step 2.1: Enhanced Symbolic Reasoning

**Update `src/symbolic_reasoning.py`:**
```python
from src.ollama_client import OllamaClient

class SymbolicReasoner:
    def __init__(self, engine: str = "local", model: str = "llama", use_gpu: bool = True):
        # ... existing code ...

        # Add Ollama support
        if engine == "ollama":
            self.ollama_client = OllamaClient()
            self.model = model or "llama3.2"

    async def _process_ollama_query(
        self,
        query: str,
        context: Optional[str] = None,
        stream: bool = False
    ) -> Union[str, AsyncGenerator]:
        """Process query using Ollama."""
        messages = []

        # Add system message for symbolic reasoning
        system_prompt = """You are an advanced AI assistant specializing in symbolic reasoning and logical analysis.
        Provide clear, step-by-step reasoning for complex problems. When possible, show your logical steps explicitly."""
        messages.append({"role": "system", "content": system_prompt})

        # Add context if provided
        if context:
            messages.append({
                "role": "system",
                "content": f"Additional context: {context}"
            })

        # Add user query
        messages.append({"role": "user", "content": query})

        # Process with Ollama
        response = await self.ollama_client.chat(
            model=self.model,
            messages=messages,
            stream=stream,
            temperature=0.7,
            top_p=0.9
        )

        if stream:
            return response
        else:
            return response['message']['content']

    async def process_query(
        self,
        query: str,
        context: Optional[str] = None,
        stream: bool = False
    ) -> Union[str, AsyncGenerator]:
        """Enhanced process_query with Ollama support."""
        if self.engine == "ollama":
            return await self._process_ollama_query(query, context, stream)
        else:
            # Existing implementation for other engines
            return self._process_local_query(query, context)
```

### Phase 4 Implementation Details

#### Step 4.1: Streaming Implementation

**Add to `src/main.py`:**
```python
from fastapi.responses import StreamingResponse
import json

@app.post("/v1/chat/completions")
async def enhanced_chat_completions(request: OpenAIChatRequest):
    """Enhanced OpenAI-compatible chat endpoint with Ollama support."""

    # Check if using Ollama engine
    settings = get_settings()
    if settings.model.reasoning_engine == "ollama":
        ollama_client = OllamaClient()

        if request.stream:
            # Return streaming response
            async def generate_stream():
                async for chunk in await ollama_client.chat(
                    model=request.model,
                    messages=[msg.dict() for msg in request.messages],
                    stream=True,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens
                ):
                    # Format as OpenAI-compatible streaming response
                    openai_chunk = {
                        "id": f"chatcmpl-{uuid.uuid4()}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": request.model,
                        "choices": [{
                            "index": 0,
                            "delta": {
                                "content": chunk.get('message', {}).get('content', '')
                            },
                            "finish_reason": None
                        }]
                    }
                    yield f"data: {json.dumps(openai_chunk)}\n\n"

                # Send final chunk
                final_chunk = {
                    "id": f"chatcmpl-{uuid.uuid4()}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": request.model,
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": "stop"
                    }]
                }
                yield f"data: {json.dumps(final_chunk)}\n\n"
                yield "data: [DONE]\n\n"

            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache"}
            )
        else:
            # Non-streaming response
            response = await ollama_client.chat(
                model=request.model,
                messages=[msg.dict() for msg in request.messages],
                stream=False,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )

            return OpenAIChatResponse(
                id=f"chatcmpl-{uuid.uuid4()}",
                object="chat.completion",
                created=int(time.time()),
                model=request.model,
                choices=[
                    OpenAIChoice(
                        index=0,
                        message=OpenAIMessage(
                            role="assistant",
                            content=response['message']['content']
                        ),
                        finish_reason="stop"
                    )
                ],
                usage=OpenAIUsage(
                    prompt_tokens=response.get('prompt_eval_count', 0),
                    completion_tokens=response.get('eval_count', 0),
                    total_tokens=response.get('prompt_eval_count', 0) + response.get('eval_count', 0)
                )
            )

    # Fallback to existing implementation
    # ... existing code ...

@app.post("/v1/embeddings")
async def generate_embeddings(request: EmbeddingRequest):
    """Generate embeddings using Ollama."""
    settings = get_settings()
    ollama_client = OllamaClient()

    # Generate embeddings
    embeddings = await ollama_client.generate_embeddings(
        model=settings.ollama.embedding_model,
        input_text=request.input
    )

    # Format as OpenAI-compatible response
    embedding_objects = []
    for i, embedding in enumerate(embeddings):
        embedding_objects.append({
            "object": "embedding",
            "embedding": embedding,
            "index": i
        })

    return {
        "object": "list",
        "data": embedding_objects,
        "model": settings.ollama.embedding_model,
        "usage": {
            "prompt_tokens": len(str(request.input)),
            "total_tokens": len(str(request.input))
        }
    }

@app.get("/v1/models")
async def list_models():
    """List available Ollama models."""
    ollama_client = OllamaClient()
    models = await ollama_client.list_models()

    # Format as OpenAI-compatible response
    model_objects = []
    for model in models:
        model_objects.append({
            "id": model.get('name', ''),
            "object": "model",
            "created": int(time.time()),
            "owned_by": "ollama"
        })

    return {
        "object": "list",
        "data": model_objects
    }
```

### Testing Implementation

**Create `tests/test_ollama_integration.py`:**
```python
import pytest
import asyncio
from src.ollama_client import OllamaClient
from src.symbolic_reasoning import SymbolicReasoner

@pytest.mark.asyncio
async def test_ollama_client_health():
    """Test Ollama client health check."""
    client = OllamaClient()
    health = await client.health_check()
    assert isinstance(health, bool)

@pytest.mark.asyncio
async def test_ollama_chat():
    """Test basic chat functionality."""
    client = OllamaClient()

    messages = [
        {"role": "user", "content": "What is 2+2?"}
    ]

    response = await client.chat(
        model="llama3.2",
        messages=messages
    )

    assert 'message' in response
    assert 'content' in response['message']
    assert len(response['message']['content']) > 0

@pytest.mark.asyncio
async def test_ollama_embeddings():
    """Test embedding generation."""
    client = OllamaClient()

    embeddings = await client.generate_embeddings(
        model="mxbai-embed-large",
        input_text="Hello world"
    )

    assert len(embeddings) == 1
    assert len(embeddings[0]) > 0
    assert all(isinstance(x, float) for x in embeddings[0])

@pytest.mark.asyncio
async def test_symbolic_reasoning_ollama():
    """Test symbolic reasoning with Ollama."""
    reasoner = SymbolicReasoner(engine="ollama", model="llama3.2")

    response = await reasoner.process_query(
        "If A implies B, and B implies C, what can we conclude about A and C?"
    )

    assert isinstance(response, str)
    assert len(response) > 0
```

---

## Quick Start Implementation Example

### Minimal Working Example (30 minutes)

For junior developers who want to see Ollama working quickly, here's a minimal implementation:

**1. Install Ollama and pull a model:**
```bash
# Install Ollama (visit ollama.com for your OS)
ollama pull llama3.2
ollama pull mxbai-embed-large
```

**2. Add to requirements.txt:**
```
ollama>=0.5.1
```

**3. Create simple test script `test_ollama.py`:**
```python
import ollama
import asyncio

async def test_ollama():
    client = ollama.AsyncClient()

    # Test chat
    response = await client.chat(
        model='llama3.2',
        messages=[{'role': 'user', 'content': 'Hello!'}]
    )
    print("Chat:", response['message']['content'])

    # Test embeddings
    embedding = await client.embed(
        model='mxbai-embed-large',
        input='Hello world'
    )
    print("Embedding length:", len(embedding['embeddings'][0]))

if __name__ == "__main__":
    asyncio.run(test_ollama())
```

**4. Run test:**
```bash
python test_ollama.py
```

If this works, you're ready to proceed with the full integration!

## Common Pitfalls and Solutions

### Issue 1: "Model not found" errors
**Solution**: Always check if model exists before using:
```python
try:
    response = await client.chat(model='llama3.2', messages=messages)
except ollama.ResponseError as e:
    if e.status_code == 404:
        print("Pulling model...")
        await client.pull('llama3.2')
        response = await client.chat(model='llama3.2', messages=messages)
```

### Issue 2: Slow responses
**Solutions**:
- Use smaller models for development (`llama3.2:1b`)
- Implement response caching
- Use streaming for better UX

### Issue 3: Memory issues
**Solutions**:
- Set `keep_alive=0` to unload models immediately
- Use model rotation for multiple models
- Monitor memory usage

### Issue 4: Integration with existing code
**Solution**: Create adapter pattern:
```python
class LLMAdapter:
    def __init__(self, engine="ollama"):
        if engine == "ollama":
            self.client = ollama.AsyncClient()
        # ... other engines

    async def generate(self, prompt, **kwargs):
        if self.engine == "ollama":
            return await self._ollama_generate(prompt, **kwargs)
        # ... other implementations
```

## Step-by-Step Debugging Guide

### Debug Step 1: Verify Ollama Installation
```bash
ollama --version
ollama list
ollama ps
```

### Debug Step 2: Test Basic Connectivity
```python
import ollama
try:
    client = ollama.Client()
    models = client.list()
    print("Connected! Available models:", [m['name'] for m in models['models']])
except Exception as e:
    print("Connection failed:", e)
```

### Debug Step 3: Test Model Loading
```python
import ollama
client = ollama.Client()
try:
    response = client.generate(model='llama3.2', prompt='Test')
    print("Model working!")
except Exception as e:
    print("Model issue:", e)
    # Try pulling the model
    client.pull('llama3.2')
```

### Debug Step 4: Test Integration Points
```python
# Test each integration point separately
from src.ollama_client import OllamaClient
from src.symbolic_reasoning import SymbolicReasoner

# Test client
client = OllamaClient()
health = await client.health_check()
print("Client health:", health)

# Test reasoner
reasoner = SymbolicReasoner(engine="ollama")
response = await reasoner.process_query("Test query")
print("Reasoner working:", bool(response))
```

## Performance Optimization Tips

### Tip 1: Model Selection
```python
# Development: Use small, fast models
DEVELOPMENT_MODELS = {
    "chat": "llama3.2:1b",
    "embedding": "nomic-embed-text"
}

# Production: Use larger, more capable models
PRODUCTION_MODELS = {
    "chat": "llama3.2:8b",
    "embedding": "mxbai-embed-large"
}
```

### Tip 2: Connection Pooling
```python
class OllamaPool:
    def __init__(self, max_connections=10):
        self.clients = [ollama.AsyncClient() for _ in range(max_connections)]
        self.available = asyncio.Queue()
        for client in self.clients:
            self.available.put_nowait(client)

    async def get_client(self):
        return await self.available.get()

    def return_client(self, client):
        self.available.put_nowait(client)
```

### Tip 3: Response Caching
```python
import hashlib
from functools import lru_cache

class CachedOllamaClient:
    def __init__(self):
        self.client = ollama.AsyncClient()
        self.cache = {}

    def _cache_key(self, model, messages):
        content = f"{model}:{str(messages)}"
        return hashlib.md5(content.encode()).hexdigest()

    async def chat(self, model, messages, **kwargs):
        if not kwargs.get('stream', False):  # Only cache non-streaming
            key = self._cache_key(model, messages)
            if key in self.cache:
                return self.cache[key]

        response = await self.client.chat(model=model, messages=messages, **kwargs)

        if not kwargs.get('stream', False):
            self.cache[key] = response

        return response
```

## Production Deployment Checklist

### Infrastructure Requirements
- [ ] **Ollama server with adequate resources**
  - 16GB+ RAM for 7B models
  - 32GB+ RAM for 13B models
  - GPU with 8GB+ VRAM (recommended)

- [ ] **Network configuration**
  - Ollama accessible from application server
  - Proper firewall rules
  - Load balancer configuration (if multiple Ollama instances)

### Configuration Management
- [ ] **Environment-specific settings**
```python
# config/production.yaml
ollama:
  host: "http://ollama-server:11434"
  default_model: "llama3.2:8b"
  embedding_model: "mxbai-embed-large"
  timeout: 300
  max_concurrent_requests: 20

# config/development.yaml
ollama:
  host: "http://localhost:11434"
  default_model: "llama3.2:1b"
  embedding_model: "nomic-embed-text"
  timeout: 60
  max_concurrent_requests: 5
```

### Monitoring Setup
- [ ] **Health check endpoints**
- [ ] **Performance metrics collection**
- [ ] **Error rate monitoring**
- [ ] **Resource usage alerts**

### Security Considerations
- [ ] **Network security** (VPN, firewall rules)
- [ ] **Input validation** and sanitization
- [ ] **Rate limiting** per user/API key
- [ ] **Audit logging** for all requests

**Note**: This checklist should be completed incrementally, testing each phase before moving to the next. Each step builds upon the previous ones, so maintain working functionality throughout the process.
