"""
Advanced caching system for the Neural Symbolic Language Model.

This module provides LRU cache with TTL support, memory management,
and optional Redis backend for distributed caching.

Author: AI Assistant
Date: 2025-06-29
"""

import time
import threading
from typing import Any, Optional, Dict, Tuple
from collections import OrderedDict
import logging
import hashlib
import json

logger = logging.getLogger(__name__)


class LRUCache:
    """Thread-safe LRU cache with TTL support."""

    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        """Initialize the LRU cache.

        Args:
            max_size: Maximum number of items to cache
            ttl_seconds: Time-to-live for cache entries in seconds
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache: OrderedDict[str, Tuple[Any, float]] = OrderedDict()
        self._lock = threading.RLock()
        self._hits = 0
        self._misses = 0

        logger.info(f"LRU Cache initialized: max_size={max_size}, ttl={ttl_seconds}s")

    def _is_expired(self, timestamp: float) -> bool:
        """Check if a cache entry is expired.

        Args:
            timestamp: Entry timestamp

        Returns:
            True if expired, False otherwise
        """
        return time.time() - timestamp > self.ttl_seconds

    def _generate_key(self, key: Any) -> str:
        """Generate a string key from any hashable object.

        Args:
            key: The key to hash

        Returns:
            String representation of the key
        """
        if isinstance(key, str):
            return key
        elif isinstance(key, (dict, list)):
            # For complex objects, create a hash
            key_str = json.dumps(key, sort_keys=True)
            return hashlib.md5(key_str.encode()).hexdigest()
        else:
            return str(key)

    def get(self, key: Any) -> Optional[Any]:
        """Get a value from the cache.

        Args:
            key: The cache key

        Returns:
            The cached value or None if not found/expired
        """
        with self._lock:
            str_key = self._generate_key(key)

            if str_key not in self._cache:
                self._misses += 1
                logger.debug(f"Cache miss for key: {str_key}")
                return None

            value, timestamp = self._cache[str_key]

            # Check if expired
            if self._is_expired(timestamp):
                del self._cache[str_key]
                self._misses += 1
                logger.debug(f"Cache expired for key: {str_key}")
                return None

            # Move to end (most recently used)
            self._cache.move_to_end(str_key)
            self._hits += 1
            logger.debug(f"Cache hit for key: {str_key}")
            return value

    def set(self, key: Any, value: Any) -> None:
        """Set a value in the cache.

        Args:
            key: The cache key
            value: The value to cache
        """
        with self._lock:
            str_key = self._generate_key(key)
            current_time = time.time()

            # If key exists, update it
            if str_key in self._cache:
                self._cache[str_key] = (value, current_time)
                self._cache.move_to_end(str_key)
                logger.debug(f"Cache updated for key: {str_key}")
                return

            # If at capacity, remove least recently used
            if len(self._cache) >= self.max_size:
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]
                logger.debug(f"Cache evicted LRU key: {oldest_key}")

            # Add new entry
            self._cache[str_key] = (value, current_time)
            logger.debug(f"Cache set for key: {str_key}")

    def delete(self, key: Any) -> bool:
        """Delete a key from the cache.

        Args:
            key: The cache key to delete

        Returns:
            True if key was deleted, False if not found
        """
        with self._lock:
            str_key = self._generate_key(key)
            if str_key in self._cache:
                del self._cache[str_key]
                logger.debug(f"Cache deleted key: {str_key}")
                return True
            return False

    def clear(self) -> None:
        """Clear all entries from the cache."""
        with self._lock:
            self._cache.clear()
            self._hits = 0
            self._misses = 0
            logger.info("Cache cleared")

    def cleanup_expired(self) -> int:
        """Remove expired entries from the cache.

        Returns:
            Number of entries removed
        """
        with self._lock:
            current_time = time.time()
            expired_keys = []

            for key, (_, timestamp) in self._cache.items():
                if current_time - timestamp > self.ttl_seconds:
                    expired_keys.append(key)

            for key in expired_keys:
                del self._cache[key]

            if expired_keys:
                logger.info(f"Cache cleanup removed {len(expired_keys)} expired entries")

            return len(expired_keys)

    def size(self) -> int:
        """Get the current cache size.

        Returns:
            Number of entries in the cache
        """
        with self._lock:
            return len(self._cache)

    def stats(self) -> Dict[str, Any]:
        """Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = self._hits / total_requests if total_requests > 0 else 0

            return {
                "size": len(self._cache),
                "max_size": self.max_size,
                "hits": self._hits,
                "misses": self._misses,
                "hit_rate": hit_rate,
                "ttl_seconds": self.ttl_seconds
            }


class CacheManager:
    """Manages multiple cache instances and provides unified interface."""

    def __init__(self, default_max_size: int = 1000, default_ttl: int = 3600):
        """Initialize the cache manager.

        Args:
            default_max_size: Default maximum size for new caches
            default_ttl: Default TTL for new caches
        """
        self.default_max_size = default_max_size
        self.default_ttl = default_ttl
        self._caches: Dict[str, LRUCache] = {}
        self._lock = threading.RLock()

        # Create default cache
        self._caches["default"] = LRUCache(default_max_size, default_ttl)

        logger.info("Cache manager initialized")

    def get_cache(self, name: str = "default") -> LRUCache:
        """Get a named cache instance.

        Args:
            name: Cache name

        Returns:
            LRUCache instance
        """
        with self._lock:
            if name not in self._caches:
                self._caches[name] = LRUCache(self.default_max_size, self.default_ttl)
                logger.info(f"Created new cache: {name}")

            return self._caches[name]

    def create_cache(self, name: str, max_size: int, ttl_seconds: int) -> LRUCache:
        """Create a new named cache with specific settings.

        Args:
            name: Cache name
            max_size: Maximum cache size
            ttl_seconds: TTL in seconds

        Returns:
            LRUCache instance
        """
        with self._lock:
            self._caches[name] = LRUCache(max_size, ttl_seconds)
            logger.info(f"Created cache '{name}': max_size={max_size}, ttl={ttl_seconds}s")
            return self._caches[name]

    def cleanup_all(self) -> Dict[str, int]:
        """Cleanup expired entries from all caches.

        Returns:
            Dictionary mapping cache names to number of entries removed
        """
        results = {}
        with self._lock:
            for name, cache in self._caches.items():
                removed = cache.cleanup_expired()
                results[name] = removed

        total_removed = sum(results.values())
        if total_removed > 0:
            logger.info(f"Cache cleanup removed {total_removed} total expired entries")

        return results

    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all caches.

        Returns:
            Dictionary mapping cache names to their statistics
        """
        with self._lock:
            return {name: cache.stats() for name, cache in self._caches.items()}


# Global cache manager instance
cache_manager = CacheManager()


def get_cache(name: str = "default") -> LRUCache:
    """Get a cache instance by name.

    Args:
        name: Cache name

    Returns:
        LRUCache instance
    """
    return cache_manager.get_cache(name)